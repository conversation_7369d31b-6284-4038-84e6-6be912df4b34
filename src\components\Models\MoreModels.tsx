import React from 'react';
import { Download, ExternalLink, Bot, Zap, Star, Building2, Globe, Brain, Palette, MessageSquare } from 'lucide-react';
import { useSEO } from '../../hooks/useSEO';

const MoreModels: React.FC = () => {
  useSEO({
    title: '更多模型 - AI模型平台',
    description: '探索更多AI模型，包括OpenAI、Claude、Google、DeepSeek、xAI、Flux、Midjourney、豆包、千问等模型的完整清单',
    keywords: 'AI模型, OpenAI, Claude, Google, DeepSeek, xAI, Flux, Midjourney, 豆包, 千问, 模型清单'
  });

  const modelCategories = [
    {
      id: 'text-models',
      title: '文本生成模型',
      icon: MessageSquare,
      description: '专业的文本生成和对话AI模型',
      color: 'bg-blue-50 border-blue-200',
      iconColor: 'text-blue-600',
      models: [
        {
          name: 'OpenAI 模型',
          description: 'GPT-4、GPT-3.5系列，业界领先的文本生成模型',
          features: ['多语言支持', '代码生成', '逻辑推理', '创意写作'],
          provider: 'OpenAI'
        },
        {
          name: '<PERSON> 模型',
          description: 'Anthropic开发的安全、有用的AI助手',
          features: ['长文本处理', '安全对话', '分析推理', '代码辅助'],
          provider: 'Anthropic'
        },
        {
          name: 'Google 模型',
          description: 'Gemini系列，Google最新的多模态AI模型',
          features: ['多模态理解', '科学计算', '实时信息', '多语言精通'],
          provider: 'Google'
        },
        {
          name: 'DeepSeek 模型',
          description: '深度求索开发的高性能推理模型',
          features: ['数学推理', '代码生成', '逻辑分析', '中文优化'],
          provider: 'DeepSeek'
        },
        {
          name: 'xAI 模型',
          description: 'Elon Musk创立的xAI公司开发的Grok模型',
          features: ['实时信息', '幽默对话', '创新思维', '多元视角'],
          provider: 'xAI'
        },
        {
          name: '豆包模型',
          description: '字节跳动开发的中文优化大语言模型',
          features: ['中文理解', '本土化服务', '多场景应用', '高效推理'],
          provider: '字节跳动'
        },
        {
          name: '千问模型',
          description: '阿里巴巴开发的通义千问大语言模型',
          features: ['中文优化', '多模态能力', '企业级应用', '知识问答'],
          provider: '阿里巴巴'
        }
      ]
    },
    {
      id: 'image-models',
      title: '图像生成模型',
      icon: Palette,
      description: '专业的AI图像生成和编辑模型',
      color: 'bg-purple-50 border-purple-200',
      iconColor: 'text-purple-600',
      models: [
        {
          name: 'Flux 模型',
          description: '先进的文本到图像生成模型，支持高质量图像创作',
          features: ['高分辨率生成', '风格控制', '快速渲染', '艺术创作'],
          provider: 'Black Forest Labs'
        },
        {
          name: 'Midjourney 模型',
          description: '业界领先的AI艺术生成平台',
          features: ['艺术风格', '创意设计', '高质量输出', '社区驱动'],
          provider: 'Midjourney'
        }
      ]
    }
  ];

  const downloadModelList = () => {
    // 创建下载链接
    const link = document.createElement('a');
    link.href = '/模型ID清单与费率(持续更新).pdf';
    link.download = '模型ID清单与费率(持续更新).pdf';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <Bot className="w-8 h-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">更多模型</h1>
        </div>
        <p className="text-lg text-gray-600 max-w-3xl mx-auto">
          探索我们平台支持的所有AI模型，包括文本生成、图像创作等多种类型的先进AI模型
        </p>
      </div>

      {/* Download Section */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
        <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
          <div className="text-center md:text-left">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">完整模型清单与费率</h2>
            <p className="text-gray-600">
              下载包含所有模型ID、详细费率和使用说明的完整PDF文档
            </p>
          </div>
          <button
            onClick={downloadModelList}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors font-medium"
          >
            <Download className="w-5 h-5" />
            <span>下载模型清单</span>
          </button>
        </div>
      </div>

      {/* Model Categories */}
      <div className="space-y-8">
        {modelCategories.map((category) => {
          const IconComponent = category.icon;
          return (
            <div key={category.id} className={`rounded-xl border p-6 ${category.color}`}>
              <div className="flex items-center space-x-3 mb-6">
                <IconComponent className={`w-6 h-6 ${category.iconColor}`} />
                <h2 className="text-2xl font-bold text-gray-900">{category.title}</h2>
              </div>
              <p className="text-gray-600 mb-6">{category.description}</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {category.models.map((model, index) => (
                  <div key={index} className="bg-white rounded-lg p-5 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="text-lg font-semibold text-gray-900">{model.name}</h3>
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                        {model.provider}
                      </span>
                    </div>
                    <p className="text-gray-600 text-sm mb-4">{model.description}</p>
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-gray-900">主要特性：</h4>
                      <div className="flex flex-wrap gap-1">
                        {model.features.map((feature, featureIndex) => (
                          <span
                            key={featureIndex}
                            className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      {/* Additional Info */}
      <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
        <div className="text-center space-y-4">
          <h2 className="text-xl font-semibold text-gray-900">需要更多信息？</h2>
          <p className="text-gray-600">
            如果您需要了解特定模型的详细配置、使用方法或定价信息，请下载完整的模型清单文档，
            或联系我们的技术支持团队获取专业指导。
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-4">
            <button
              onClick={downloadModelList}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>下载完整清单</span>
            </button>
            <a
              href="#contact"
              className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors"
            >
              <ExternalLink className="w-4 h-4" />
              <span>联系技术支持</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MoreModels;
