import React, { useState } from 'react';
import { ShoppingCart, Star, Zap, DollarSign, Image as ImageIcon, Bell, X, AlertCircle, Info, CheckCircle2 } from 'lucide-react';
import { useSEO } from '../../hooks/useSEO';

const PackageIntroduction: React.FC = () => {
  // 设置页面SEO
  useSEO('packages');

  // 通知公告数据
  const notices = [
    {
      id: 'new-year-2025',
      type: 'info' as const,
      title: '🎉 性价比之王',
      content: '模型ID：gpt-4o，性价比最高的常规模型，稳定量大',
      showDate: '2025-06-01',
      priority: 1
    },
    {
      id: 'system-maintenance',
      type: 'warning' as const,
      title: '🔥编程最强模型',
      content: '模型ID：claude-sonnet-4-20250514， 编程最强模型，289tokens/s',
      showDate: '2025-6-15',
      priority: 2
    },
    {
      id: 'new-models',
      type: 'success' as const,
      title: '✨ 文案撰写最强模型',
      content: '模型ID：gemini-2.5-pro-preview-06-05，谷歌的最强模型-听说在文案撰写上风格独特！154tokens/s',
      showDate: '2025-06-10',
      priority: 3
    },
      {
      id: 'new-models',
      type: 'success' as const,
      title: '✨ 性价比最高的推理模型',
      content: '模型ID：o3，OpenAi性价比最高的推理模型-逻辑能力优异！¥0.42/次',
      showDate: '2025-06-10',
      priority: 4
    },
         {
      id: 'new-models',
      type: 'success' as const,
      title: '✨ 图像生成模型推荐',
      content: '模型ID：gpt-image-1,gpt-4o-image-vip,gpt-4o-image',
      showDate: '2025-06-10',
      priority: 5
    }
  ];

  // 获取通知图标
  const getNoticeIcon = (type: 'info' | 'warning' | 'success') => {
    switch (type) {
      case 'info':
        return Info;
      case 'warning':
        return AlertCircle;
      case 'success':
        return CheckCircle2;
      default:
        return Bell;
    }
  };

  // 获取通知样式
  const getNoticeStyle = (type: 'info' | 'warning' | 'success') => {
    switch (type) {
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  // 按优先级排序的通知
  const activeNotices = notices.sort((a, b) => a.priority - b.priority);

  const packages = [
    { category: 'AI 全模型', name: '1 刀体验套餐', quota: '$1', discount: '0.9', price: '0.9元', note: '', purchaseUrl: 'https://h5.m.goofish.com/item?id=937583951559' },
    { category: 'AI全模型', name: '10刀套餐', quota: '$10', discount: '0.9', price: '9 元', note: '', purchaseUrl: 'https://h5.m.goofish.com/item?id=938007762192' },
    { category: 'AI 全模型', name: '20 刀套餐', quota: '$20', discount: '0.85', price: '17元', note: '👍推荐', highlight: true, purchaseUrl: 'https://h5.m.goofish.com/item?id=945371019429' },
    { category: 'AI全模型', name: '50刀套餐', quota: '$50', discount: '0.8', price: '40 元', note: '💯优惠力度大', highlight: true, purchaseUrl: 'https://h5.m.goofish.com/item?id=937526871723' },
    { category: 'DeepSeek 专属', name: '1 元体验套餐', quota: '￥1', discount: '0.8', price: '0.8 元', note: '', purchaseUrl: 'https://h5.m.goofish.com/item?id=937957770190' },
    { category: 'DeepSeek 专属', name: '10 元限时套餐', quota: '￥10', discount: '0.66', price: '6.66 元', note: '😘限时优惠，推荐体验', highlight: true, purchaseUrl: 'https://h5.m.goofish.com/item?id=936991394373' }
  ];

  const ImagePlaceholder = ({ title, description }: { title: string; description?: string }) => (
    <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
      <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-3" />
      <h4 className="text-sm font-medium text-gray-600 mb-1">{title}</h4>
      {description && <p className="text-xs text-gray-500">{description}</p>}
    </div>
  );

  const XianYuShopImage = () => (
    <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
      <img
        src="/xianyu-shop.png"
        alt="闲鱼店铺截图"
        className="w-full h-auto object-contain"
        onError={(e) => {
          // 如果图片加载失败，显示占位符
          const target = e.target as HTMLImageElement;
          target.style.display = 'none';
          const parent = target.parentElement;
          if (parent && !parent.querySelector('.fallback-content')) {
            const fallback = document.createElement('div');
            fallback.className = 'fallback-content bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center';
            fallback.innerHTML = `
              <div class="w-12 h-12 mx-auto mb-3 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </div>
              <h4 class="text-sm font-medium text-gray-600 mb-1">闲鱼店铺截图</h4>
              <p class="text-xs text-gray-500">请将图片保存为 xianyu-shop.png 并放置到 public 目录</p>
            `;
            parent.appendChild(fallback);
          }
        }}
      />
    </div>
  );

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl p-6 md:p-8 text-white">
        <div className="flex flex-col md:flex-row md:items-center mb-6">
          <div className="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mb-4 md:mb-0 md:mr-4">
            <ShoppingCart className="w-8 h-8" />
          </div>
          <div>
            <h1 className="text-2xl md:text-3xl font-bold mb-2">AI模型套餐介绍</h1>
            <p className="text-blue-100 text-base md:text-lg">选择适合您需求的AI模型套餐，享受优质的AI服务</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
          <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
            <div className="flex items-center mb-2">
              <Star className="w-5 h-5 text-yellow-300 mr-2" />
              <span className="font-semibold">全模型支持</span>
            </div>
            <p className="text-sm text-blue-100">支持OpenAI、Claude、Gemini等主流AI模型</p>
          </div>
          <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
            <div className="flex items-center mb-2">
              <Zap className="w-5 h-5 text-yellow-300 mr-2" />
              <span className="font-semibold">高性价比</span>
            </div>
            <p className="text-sm text-blue-100">最低0.66折优惠，让AI使用更经济实惠</p>
          </div>
          <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
            <div className="flex items-center mb-2">
              <DollarSign className="w-5 h-5 text-yellow-300 mr-2" />
              <span className="font-semibold">灵活计费</span>
            </div>
            <p className="text-sm text-blue-100">按需购买，支持体验套餐到大额度套餐</p>
          </div>
        </div>
      </div>

      {/* Package Table and Notices */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：闲鱼店铺套餐 */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden h-full">
            <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
              <div className="flex items-center mb-4">
                <ShoppingCart className="w-6 h-6 text-blue-600 mr-3" />
                <h2 className="text-2xl font-bold text-gray-900">闲鱼店铺套餐</h2>
              </div>
              
              <div className="mb-6">
                <XianYuShopImage />
              </div>
            </div>

            <div className="p-6">
              {/* 桌面端表格 */}
              <div className="hidden lg:block">
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse bg-white">
                    <thead>
                      <tr className="bg-gradient-to-r from-gray-50 to-gray-100">
                        <th className="px-4 py-4 text-left text-sm font-semibold text-gray-900 border-b-2 border-gray-200 w-[12%]">类别</th>
                        <th className="px-4 py-4 text-left text-sm font-semibold text-gray-900 border-b-2 border-gray-200 w-[20%]">套餐名称</th>
                        <th className="px-4 py-4 text-center text-sm font-semibold text-gray-900 border-b-2 border-gray-200 w-[12%]">额度</th>
                        <th className="px-4 py-4 text-center text-sm font-semibold text-gray-900 border-b-2 border-gray-200 w-[10%]">折扣</th>
                        <th className="px-4 py-4 text-center text-sm font-semibold text-gray-900 border-b-2 border-gray-200 w-[15%]">实付金额</th>
                        <th className="px-4 py-4 text-left text-sm font-semibold text-gray-900 border-b-2 border-gray-200 w-[21%]">备注</th>
                        <th className="px-4 py-4 text-center text-sm font-semibold text-gray-900 border-b-2 border-gray-200 w-[10%]">购买</th>
                      </tr>
                    </thead>
                    <tbody>
                      {packages.map((pkg, index) => (
                        <tr
                          key={index}
                          className={`${
                            pkg.highlight
                              ? 'bg-gradient-to-r from-yellow-50 to-orange-50 hover:from-yellow-100 hover:to-orange-100 border-l-4 border-l-yellow-400'
                              : 'hover:bg-gray-50'
                          } transition-all duration-200 border-b border-gray-100`}
                        >
                          <td className="px-4 py-4">
                            <span className="text-sm text-gray-700 font-medium">{pkg.category}</span>
                          </td>
                          <td className="px-4 py-4">
                            <div className="flex items-center">
                              {pkg.highlight && <Star className="w-4 h-4 text-yellow-500 mr-2 flex-shrink-0" />}
                              <span className="text-sm font-semibold text-gray-900">{pkg.name}</span>
                            </div>
                          </td>
                          <td className="px-4 py-4 text-center">
                            <span className="inline-flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs font-semibold">
                              {pkg.quota}
                            </span>
                          </td>
                          <td className="px-4 py-4 text-center">
                            <span className="inline-flex items-center bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-semibold">
                              {pkg.discount}
                            </span>
                          </td>
                          <td className="px-4 py-4 text-center">
                            <span className="text-lg font-bold text-green-600">{pkg.price}</span>
                          </td>
                          <td className="px-4 py-4">
                            {pkg.note && (
                              <span className={`${pkg.highlight ? 'text-red-600 font-semibold' : 'text-gray-600'} text-sm`}>
                                {pkg.note}
                              </span>
                            )}
                          </td>
                          <td className="px-4 py-4 text-center">
                            <a
                              href={pkg.purchaseUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md"
                            >
                              购买
                            </a>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* 平板端紧凑表格 */}
              <div className="hidden md:block lg:hidden">
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse bg-white">
                    <thead>
                      <tr className="bg-gradient-to-r from-gray-50 to-gray-100">
                        <th className="px-3 py-3 text-left text-sm font-semibold text-gray-900 border-b-2 border-gray-200">套餐</th>
                        <th className="px-3 py-3 text-center text-sm font-semibold text-gray-900 border-b-2 border-gray-200">额度/折扣</th>
                        <th className="px-3 py-3 text-center text-sm font-semibold text-gray-900 border-b-2 border-gray-200">价格</th>
                        <th className="px-3 py-3 text-center text-sm font-semibold text-gray-900 border-b-2 border-gray-200">操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      {packages.map((pkg, index) => (
                        <tr
                          key={index}
                          className={`${
                            pkg.highlight
                              ? 'bg-gradient-to-r from-yellow-50 to-orange-50 hover:from-yellow-100 hover:to-orange-100 border-l-4 border-l-yellow-400'
                              : 'hover:bg-gray-50'
                          } transition-all duration-200 border-b border-gray-100`}
                        >
                          <td className="px-3 py-4">
                            <div>
                              <div className="flex items-center mb-1">
                                {pkg.highlight && <Star className="w-4 h-4 text-yellow-500 mr-1" />}
                                <span className="text-sm font-semibold text-gray-900">{pkg.name}</span>
                              </div>
                              <span className="text-xs text-gray-600">{pkg.category}</span>
                              {pkg.note && (
                                <div className="mt-1">
                                  <span className={`${pkg.highlight ? 'text-red-600 font-semibold' : 'text-gray-600'} text-xs`}>
                                    {pkg.note}
                                  </span>
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="px-3 py-4 text-center">
                            <div className="space-y-1">
                              <span className="inline-flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                {pkg.quota}
                              </span>
                              <br />
                              <span className="inline-flex items-center bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                                {pkg.discount}
                              </span>
                            </div>
                          </td>
                          <td className="px-3 py-4 text-center">
                            <span className="text-lg font-bold text-green-600">{pkg.price}</span>
                          </td>
                          <td className="px-3 py-4 text-center">
                            <a
                              href={pkg.purchaseUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors duration-200"
                            >
                              购买
                            </a>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* 移动端卡片布局 */}
              <div className="md:hidden space-y-3">
                {packages.map((pkg, index) => (
                  <div
                    key={index}
                    className={`${
                      pkg.highlight
                        ? 'bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-300 ring-2 ring-yellow-200'
                        : 'bg-white border-gray-200'
                    } border rounded-xl shadow-sm hover:shadow-md transition-all duration-200`}
                  >
                    {/* 卡片头部 */}
                    <div className="p-4 pb-3">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center mb-1">
                            {pkg.highlight && <Star className="w-4 h-4 text-yellow-500 mr-2 flex-shrink-0" />}
                            <h3 className="text-lg font-bold text-gray-900">{pkg.name}</h3>
                          </div>
                          <p className="text-sm text-gray-600">{pkg.category}</p>
                        </div>
                        {pkg.highlight && (
                          <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-semibold ml-2">
                            推荐
                          </span>
                        )}
                      </div>

                      {/* 价格突出显示 */}
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-3">
                        <div className="text-center">
                          <p className="text-xs text-green-600 mb-1">实付金额</p>
                          <span className="text-2xl font-bold text-green-600">{pkg.price}</span>
                        </div>
                      </div>

                      {/* 套餐信息网格 */}
                      <div className="grid grid-cols-2 gap-3 mb-3">
                        <div className="text-center">
                          <p className="text-xs text-gray-500 mb-2">额度</p>
                          <span className="inline-flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold">
                            {pkg.quota}
                          </span>
                        </div>
                        <div className="text-center">
                          <p className="text-xs text-gray-500 mb-2">折扣</p>
                          <span className="inline-flex items-center bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold">
                            {pkg.discount}
                          </span>
                        </div>
                      </div>

                      {/* 备注信息 */}
                      {pkg.note && (
                        <div className="mb-3">
                          <div className={`${
                            pkg.highlight
                              ? 'bg-red-50 border-red-200 text-red-700'
                              : 'bg-gray-50 border-gray-200 text-gray-600'
                          } border rounded-lg p-2 text-center`}>
                            <span className="text-sm font-medium">{pkg.note}</span>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* 购买按钮 */}
                    <div className="px-4 pb-4">
                      <a
                        href={pkg.purchaseUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-full inline-flex items-center justify-center px-4 py-3 text-base font-semibold text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md"
                      >
                        <ShoppingCart className="w-4 h-4 mr-2" />
                        立即购买
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 右侧：通知公告 */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 h-full">
            <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-yellow-50 to-orange-50">
              <div className="flex items-center mb-4">
                <Bell className="w-6 h-6 text-orange-600 mr-3" />
                <h2 className="text-2xl font-bold text-gray-900">通知公告</h2>
              </div>
            </div>
            
            <div className="p-6">
              <div className="space-y-4">
                {activeNotices.map((notice) => {
                  const IconComponent = getNoticeIcon(notice.type);
                  return (
                    <div
                      key={notice.id}
                      className={`${getNoticeStyle(notice.type)} border rounded-lg p-4`}
                    >
                      <div className="flex items-start">
                        <div className="flex-shrink-0 mr-3">
                          <IconComponent className="w-4 h-4 mt-0.5" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-semibold mb-2">{notice.title}</h4>
                          <p className="text-xs opacity-90 leading-relaxed">{notice.content}</p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Package Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
              <Zap className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">全模型套餐</h3>
              <p className="text-sm text-gray-600">支持所有主流AI模型</p>
            </div>
          </div>
          <ul className="space-y-2 text-sm text-gray-700">
            <li className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              支持OpenAI全系列模型（GPT-4、GPT-3.5等）
            </li>
            <li className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              支持Claude全系列模型（Claude-4、Claude-3等）
            </li>
            <li className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              支持Google Gemini系列模型
            </li>
            <li className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              支持DeepSeek、xAI等其他模型
            </li>
          </ul>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
              <Star className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">DeepSeek专属</h3>
              <p className="text-sm text-gray-600">专门优化的DeepSeek套餐</p>
            </div>
          </div>
          <ul className="space-y-2 text-sm text-gray-700">
            <li className="flex items-center">
              <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
              专门针对DeepSeek模型优化
            </li>
            <li className="flex items-center">
              <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
              更优惠的价格，最低6.66元
            </li>
            <li className="flex items-center">
              <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
              支持deepseek-r1、deepseek-v3等模型
            </li>
            <li className="flex items-center">
              <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
              适合对DeepSeek有特殊需求的用户
            </li>
          </ul>
        </div>
      </div>

      {/* Purchase Guide */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
        <div className="flex items-start">
          <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
            <ShoppingCart className="w-6 h-6 text-green-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-green-900 mb-3">购买指南</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-green-800">
              <div>
                <h4 className="font-medium mb-2">🎯 新手推荐</h4>
                <p>建议先购买<strong>1刀体验套餐</strong>或<strong>1元体验套餐</strong>，熟悉使用流程后再购买大额度套餐。</p>
              </div>
              <div>
                <h4 className="font-medium mb-2">💰 性价比之选</h4>
                <p><strong>20刀套餐</strong>和<strong>10元限时套餐</strong>折扣力度大，适合有一定使用量的用户。</p>
              </div>
              <div>
                <h4 className="font-medium mb-2">🚀 重度用户</h4>
                <p><strong>50刀套餐</strong>适合企业用户或重度个人用户，享受最大优惠力度。</p>
              </div>
              <div>
                <h4 className="font-medium mb-2">🎁 限时优惠</h4>
                <p>关注<strong>DeepSeek专属套餐</strong>的限时优惠，性价比极高。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PackageIntroduction;
