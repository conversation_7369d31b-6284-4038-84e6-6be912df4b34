import React, { useState } from 'react';
import { 
  Globe, 
  List, 
  HelpCircle, 
  RefreshCw,
  Copy,
  ExternalLink,
  CheckCircle,
  AlertTriangle,
  Info,
  Star,
  Zap,
  Settings,
  Download,
  Monitor,
  ChevronRight,
  ChevronDown,
  Image as ImageIcon,
  Languages,
  MessageSquare,
  Bot,
  Gamepad2
} from 'lucide-react';
import { useSEO } from '../../hooks/useSEO';
import { useApp } from '../../contexts/AppContext';

const Tutorial: React.FC = () => {
  // 设置页面SEO
  useSEO('tutorial');
  const { state, dispatch } = useApp();
  const [expandedHelp, setExpandedHelp] = useState(false);

  const activeSection = state.tutorialSection;
  const activeHelpTool = state.tutorialClient;

  const setActiveSection = (section: string) => {
    dispatch({ type: 'SET_TUTORIAL_SECTION', payload: section });
  };

  const setActiveHelpTool = (client: string) => {
    dispatch({ type: 'SET_TUTORIAL_CLIENT', payload: client });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const sections = [
    { id: 'api', label: 'API地址', icon: Globe },
    { id: 'help', label: '客户端配置', icon: HelpCircle },
    { id: 'models', label: '推荐模型', icon: List },
    { id: 'updates', label: '联系购买', icon: RefreshCw }
  ];

  const helpTools = [
    { id: 'immersive-translate', label: '沉浸式翻译插件', icon: Languages },
    { id: 'cherry-studio', label: 'Cherry Studio（推荐）', icon: MessageSquare },
    { id: 'chatbox', label: 'ChatBoX', icon: Bot },
    { id: 'sillytavern', label: 'SillyTavern（酒馆）', icon: Gamepad2 },
    { id: 'more-clients', label: '更多客户端', icon: Monitor }
  ];

  const recommendedModels = [
    { 
      name: 'Claude', 
      ids: ['claude-sonnet-4-20250514', 'claude-3-7-sonnet-20250219', 'claude-opus-4-20250514', 'claude-sonnet-4-20250514-thinking'], 
      note: '文字' 
    },
    { 
      name: 'deepseek', 
      ids: ['deepseek-r1-0528', 'deepseek-v3-0324'], 
      note: '' 
    },
    { 
      name: 'gemini', 
      ids: ['gemini-2.5-pro-preview-06-05', 'gemini-2.5-flash-preview-04-17'], 
      note: '' 
    },
    { 
      name: 'chatgpt', 
      ids: ['gpt-4o', 'gpt-4.1', 'gpt-4o-mini'], 
      note: '' 
    }
  ];

  const ImagePlaceholder = ({ title, description }: { title: string; description?: string }) => (
    <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
      <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-3" />
      <h4 className="text-sm font-medium text-gray-600 mb-1">{title}</h4>
      {description && <p className="text-xs text-gray-500">{description}</p>}
    </div>
  );

  const renderApiAddress = () => (
    <div className="space-y-6">
      <div className="bg-white border border-gray-200 rounded-xl p-6">
        <div className="flex items-center mb-4">
          <Globe className="w-6 h-6 text-green-600 mr-2" />
          <h3 className="text-xl font-semibold text-gray-900">API 地址</h3>
        </div>
        <div className="bg-gray-900 text-green-400 rounded-lg p-4 font-mono text-lg relative">
          <div className="flex justify-between items-center">
            <span className="break-all">https://api.sydney-ai.com</span>
            <button
              onClick={() => copyToClipboard('https://api.sydney-ai.com')}
              className="text-gray-400 hover:text-white transition-colors ml-2 flex-shrink-0"
            >
              <Copy className="w-5 h-5" />
            </button>
          </div>
        </div>
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start">
            <Info className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
            <p className="text-blue-800 text-sm">
              API 地址需要在<strong>客户端</strong>配置使用，详见下方"客户端配置"部分。
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderModels = () => (
    <div className="space-y-6">
      <div className="bg-white border border-gray-200 rounded-xl p-6">
        <div className="flex items-center mb-4">
          <List className="w-6 h-6 text-purple-600 mr-2" />
          <h3 className="text-xl font-semibold text-gray-900">推荐模型</h3>
        </div>
        <div className="space-y-4">
          {recommendedModels.map((model, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-semibold text-gray-900 text-lg">{model.name}</h4>
                {model.note && (
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                    {model.note}
                  </span>
                )}
              </div>
              <div className="space-y-2">
                {model.ids.map((id, idIndex) => (
                  <div key={idIndex} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                    <code className="text-sm font-mono text-gray-800 break-all mr-2">{id}</code>
                    <button
                      onClick={() => copyToClipboard(id)}
                      className="text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
        <div className="flex items-center mb-4">
          <AlertTriangle className="w-6 h-6 text-yellow-600 mr-2" />
          <h3 className="text-lg font-semibold text-yellow-800">套餐说明</h3>
        </div>
        <div className="space-y-3 text-yellow-800">
          <div className="flex items-start">
            <Star className="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <strong>deepseek 专属 KEY：</strong>
              <p className="text-sm mt-1">如果购买的是 deepseek 专属 KEY（包括 1 元体验、10 元套餐等），只能使用 deepseek 的 deepseek-r1-0528, deepseek-v3-0324。</p>
            </div>
          </div>
          <div className="flex items-start">
            <Zap className="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <strong>全模型 KEY：</strong>
              <p className="text-sm mt-1">如果购买的是全模型 KEY，那么以上模型列表全部可使用。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderImmersiveTranslate = () => (
    <div className="space-y-6">
      <div className="bg-white border border-gray-200 rounded-xl p-6">
        <div className="flex items-center mb-4">
          <Languages className="w-6 h-6 text-blue-600 mr-2" />
          <h3 className="text-xl font-semibold text-gray-900">沉浸式翻译插件</h3>
        </div>
        <p className="text-gray-600 mb-6">翻译建议使用 <strong>deepseek-v3</strong>，经济实惠</p>
        
        <div className="space-y-6">
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">1. 进入设置</h4>
            <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/immersive-translate-step1.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const parent = target.parentElement;
          
                }}
              />
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">2. 添加服务</h4>
             <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/immersive-translate-step2.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"

              />
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">3. 详细配置</h4>
            <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/immersive-translate-step3.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">4. 可以看到配置的服务</h4>
            <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/immersive-translate-step4.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">5. 返回页面，选择新建的服务</h4>
            <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/immersive-translate-step5.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
        
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderCherryStudio = () => (
    <div className="space-y-6">
      <div className="bg-white border border-gray-200 rounded-xl p-6">
        <div className="flex items-center mb-4">
          <MessageSquare className="w-6 h-6 text-green-600 mr-2" />
          <h3 className="text-xl font-semibold text-gray-900">Cherry Studio（推荐）</h3>
          <span className="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">推荐</span>
        </div>
        
        <div className="mb-4">
          <a
            href="https://www.cherry-ai.com/"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-blue-600 hover:text-blue-700 break-all"
          >
            <ExternalLink className="w-4 h-4 mr-1 flex-shrink-0" />
            https://www.cherry-ai.com/
          </a>
        </div>
        
        <div className="space-y-6">
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">1. 下载安装客户端</h4>
            <div className="grid grid-cols-1 gap-4">
                          <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/cherry-studio-step1.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
             <h4 className="font-semibold text-gray-900 mb-3">客户端安装完成后，打开客户端：</h4>
                          <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/cherry-studio-step2.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">2. 填写名称</h4>
                        <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/cherry-studio-step3.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">3. 填写密钥、地址</h4>
                        <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/cherry-studio-step4.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">4. 添加模型，支持批量添加</h4>
             <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/cherry-studio-step5.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">5. 模型检测</h4>
            <div className="space-y-4">
                          <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/cherry-studio-step6.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
                          <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/cherry-studio-step7.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderChatBox = () => (
    <div className="space-y-6">
      <div className="bg-white border border-gray-200 rounded-xl p-6">
        <div className="flex items-center mb-4">
          <Bot className="w-6 h-6 text-purple-600 mr-2" />
          <h3 className="text-xl font-semibold text-gray-900">ChatBoX</h3>
        </div>
        
        <div className="mb-4 space-y-2">
          <p className="text-gray-600">chatbox 可使用网页端和客户端</p>
          <div className="space-y-2">
            <a
              href="https://chatboxai.app/zh"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-blue-600 hover:text-blue-700 break-all"
            >
              <ExternalLink className="w-4 h-4 mr-1 flex-shrink-0" />
              官网地址
            </a>
            <br />
            <a
              href="https://web.chatboxai.app/"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-green-600 hover:text-green-700 break-all"
            >
              <Monitor className="w-4 h-4 mr-1 flex-shrink-0" />
              web 端地址
            </a>
          </div>
        </div>
        
        <div className="space-y-6">
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">1. 打开应用</h4>
            <div className="grid grid-cols-1 gap-4">
            
                          <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/chatbox-step1.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">2. 输入 key、api 地址、添加模型</h4>
           <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/chatbox-step2.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">3. 新建模型</h4>
                         <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/chatbox-step3.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">4. 使用</h4>
                        <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/chatbox-step4.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSillyTavern = () => (
    <div className="space-y-6">
      <div className="bg-white border border-gray-200 rounded-xl p-6">
        <div className="flex items-center mb-4">
          <Gamepad2 className="w-6 h-6 text-pink-600 mr-2" />
          <h3 className="text-xl font-semibold text-gray-900">SillyTavern（傻乎乎的酒馆）</h3>
        </div>
        
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-800 text-sm">
            SillyTavern（傻乎乎的酒馆）是一款开源的本地 LLM（大型语言模型）前端应用，专为创意用户打造，支持多种 AI 模型和 API。它可以在 Windows、MacOS、Linux、Android（通过 Termux）等多个平台运行，提供了一个独特的交互界面，适合与虚拟角色聊天、角色扮演或创作故事。
          </p>
        </div>
        
        <div className="mb-6 space-y-2">
          <div className="space-y-2">
            <a
              href="https://github.com/SillyTavern/SillyTavern"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-blue-600 hover:text-blue-700 break-all"
            >
              <ExternalLink className="w-4 h-4 mr-1 flex-shrink-0" />
              GitHub 地址
            </a>
            <br />
            <a
              href="https://pt4300.github.io/Sillytavern-docker-tutorial/simple_docker/"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-green-600 hover:text-green-700 break-all"
            >
              <ExternalLink className="w-4 h-4 mr-1 flex-shrink-0" />
              一键部署教程
            </a>
          </div>
        </div>
        
        <div className="space-y-6">
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">配置步骤：</h4>
            <ol className="space-y-4">
              <li className="flex items-start">
                <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5 flex-shrink-0">1</span>
                <span className="text-gray-700">根据讲解安装完毕后，进入目录，运行 start.bat（Windows）或 start.sh（Linux/MacOS）完成初始化</span>
              </li>
              <li className="flex items-start">
                <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5 flex-shrink-0">2</span>
                <span className="text-gray-700">启动应用后，点击顶部的设置图标，进入 API 配置页面</span>
              </li>
              <li className="flex items-start">
                <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5 flex-shrink-0">3</span>
                <span className="text-gray-700">在 "API 提供方" 中选择 聊天补全：自定义 openAI 兼容 选项，输入API密钥并设置相关参数</span>
              </li>
            </ol>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">配置界面截图：</h4>
                        <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/sillytavern-config.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
          </div>
          
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-red-600 mr-2 mt-0.5 flex-shrink-0" />
              <div>
                <h5 className="font-medium text-red-800 mb-2">特别说明</h5>
                <p className="text-red-700 text-sm">
                  模型 ID 不同，费率不同，请选择适合您的模型 ID。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderMoreClients = () => (
    <div className="space-y-6">
      <div className="bg-white border border-gray-200 rounded-xl p-6">
        <div className="flex items-center mb-4">
          <Monitor className="w-6 h-6 text-gray-600 mr-2" />
          <h3 className="text-xl font-semibold text-gray-900">更多客户端</h3>
        </div>
        
        <div className="mb-6">
                       <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/tutorial-final-step.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
        </div>
        
        <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="flex items-start">
            <Settings className="w-5 h-5 text-gray-600 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <h5 className="font-medium text-gray-900 mb-2">通用配置流程</h5>
              <p className="text-gray-600 text-sm">
                更多客户端的基本操作都是：进入设置 → 自定义添加模型 → 设置 APIKEY、API 地址 → 添加模型 ID。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderHelpContent = () => {
    switch (activeHelpTool) {
      case 'immersive-translate':
        return renderImmersiveTranslate();
      case 'cherry-studio':
        return renderCherryStudio();
      case 'chatbox':
        return renderChatBox();
      case 'sillytavern':
        return renderSillyTavern();
      case 'more-clients':
        return renderMoreClients();
      default:
        return renderImmersiveTranslate();
    }
  };

  const renderHelp = () => (
    <div className="space-y-6">
      {/* Mobile: Vertical Help Tools Menu */}
      <div className="lg:hidden">
        <div className="bg-white border border-gray-200 rounded-xl p-4">
          <button
            onClick={() => setExpandedHelp(!expandedHelp)}
            className="w-full flex items-center justify-between text-left"
          >
            <div className="flex items-center">
              <HelpCircle className="w-5 h-5 text-blue-600 mr-2" />
              <span className="font-medium text-gray-900">选择客户端配置教程</span>
            </div>
            {expandedHelp ? (
              <ChevronDown className="w-5 h-5 text-gray-400" />
            ) : (
              <ChevronRight className="w-5 h-5 text-gray-400" />
            )}
          </button>
          
          {expandedHelp && (
            <div className="mt-4 space-y-2">
              {helpTools.map((tool) => {
                const Icon = tool.icon;
                return (
                  <button
                    key={tool.id}
                    onClick={() => {
                      setActiveHelpTool(tool.id);
                      setExpandedHelp(false);
                    }}
                    className={`w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-sm transition-colors ${
                      activeHelpTool === tool.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="w-4 h-4 flex-shrink-0" />
                    <span>{tool.label}</span>
                  </button>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Desktop: Horizontal Help Tools Menu */}
      <div className="hidden lg:block">
        <div className="bg-white border border-gray-200 rounded-xl p-4">
          <h4 className="text-sm font-semibold text-gray-900 mb-3">选择客户端配置教程</h4>
          <div className="flex flex-wrap gap-2">
            {helpTools.map((tool) => {
              const Icon = tool.icon;
              return (
                <button
                  key={tool.id}
                  onClick={() => setActiveHelpTool(tool.id)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm transition-colors ${
                    activeHelpTool === tool.id
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span className="whitespace-nowrap">{tool.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Help Content */}
      {renderHelpContent()}
    </div>
  );

  const renderUpdates = () => (
    <div className="space-y-6">
      <div className="bg-white border border-gray-200 rounded-xl p-6">
        <div className="flex items-center mb-4">
          <RefreshCw className="w-6 h-6 text-orange-600 mr-2" />
          <h3 className="text-xl font-semibold text-gray-900">联系购买</h3>
        </div>
        <div className="space-y-4">
          <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-start">
              <Info className="w-5 h-5 text-orange-600 mr-2 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-orange-800 text-sm">
                  模型 ID 随供应商及时更新，若供应商更新，请联系群主提供新的模型 ID。
                </p>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
                        <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <img
                src="/xianyu-shop.png"
                alt="进入设置截图"
                className="w-full h-auto object-contain"
              />
            </div>
          </div>
          
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start">
              <CheckCircle className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
              <div>
                <h5 className="font-medium text-blue-800 mb-2">购买流程</h5>
                <ol className="text-blue-700 text-sm space-y-1">
                  <li>1. 关注群内套餐信息</li>
                  <li>2. 选择适合的套餐类型</li>
                  <li>3. 联系群主进行购买</li>
                  <li>4. 获取API密钥开始使用</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'api':
        return renderApiAddress();
      case 'models':
        return renderModels();
      case 'help':
        return renderHelp();
      case 'updates':
        return renderUpdates();
      default:
        return renderApiAddress();
    }
  };

  return (
    <div className="w-full">
      {/* Content */}
      <div className="w-full">
        <div className="max-w-6xl mx-auto">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default Tutorial;