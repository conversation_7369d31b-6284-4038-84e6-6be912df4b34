import React, { useState } from 'react';
import { Plus, Search, Copy, Eye, EyeOff, MoreVertical, Shield, Clock, ExternalLink, BarChart3 } from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { ApiKey } from '../../types';
import ApiKeyForm from './ApiKeyForm';
import { useSEO } from '../../hooks/useSEO';

const ApiKeyManagement: React.FC = () => {
  // 设置页面SEO
  useSEO('api-keys');

  const { state, dispatch } = useApp();
  const [showForm, setShowForm] = useState(false);
  const [editingKey, setEditingKey] = useState<ApiKey | null>(null);
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');

  const filteredKeys = state.apiKeys.filter(key =>
    key.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddKey = () => {
    setEditingKey(null);
    setShowForm(true);
  };

  const handleEditKey = (key: ApiKey) => {
    setEditingKey(key);
    setShowForm(true);
  };

  const handleDeleteKey = (keyId: string) => {
    if (confirm('确定要删除这个API密钥吗？')) {
      dispatch({ type: 'DELETE_API_KEY', payload: keyId });
    }
  };

  const toggleKeyVisibility = (keyId: string) => {
    const newVisible = new Set(visibleKeys);
    if (newVisible.has(keyId)) {
      newVisible.delete(keyId);
    } else {
      newVisible.add(keyId);
    }
    setVisibleKeys(newVisible);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-red-100 text-red-800';
      case 'expired': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '活跃';
      case 'inactive': return '停用';
      case 'expired': return '已过期';
      default: return status;
    }
  };

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case 'admin': return 'bg-red-100 text-red-800';
      case 'write': return 'bg-blue-100 text-blue-800';
      case 'read': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPermissionText = (permission: string) => {
    switch (permission) {
      case 'admin': return '管理员';
      case 'write': return '写入';
      case 'read': return '只读';
      default: return permission;
    }
  };

  return (
    <div className="space-y-6">
      {/* Usage Check Website Link */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-8">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <BarChart3 className="w-8 h-8 text-blue-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-blue-900 mb-3">API 使用量查询</h3>
          <p className="text-blue-700 mb-6 text-lg">实时查询您的API密钥使用情况和剩余额度</p>
          
          <div className="bg-white rounded-lg p-6 mb-6 shadow-sm">
            <div className="flex items-center justify-center space-x-4 mb-4">
              <div className="bg-gray-100 rounded-lg px-4 py-2 font-mono text-lg text-gray-800">
                https://check.sydney-ai.com
              </div>
              <button
                onClick={() => copyToClipboard('https://check.sydney-ai.com')}
                className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100"
                title="复制链接"
              >
                <Copy className="w-5 h-5" />
              </button>
            </div>
            
            <a
              href="https://check.sydney-ai.com"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors text-lg font-medium"
            >
              <ExternalLink className="w-5 h-5" />
              <span>打开使用量查询网站</span>
            </a>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="bg-white/70 rounded-lg p-4">
              <div className="flex items-center justify-center mb-2">
                <Shield className="w-5 h-5 text-green-600" />
              </div>
              <h4 className="font-semibold text-gray-800 mb-1">安全查询</h4>
              <p className="text-gray-600">输入您的API密钥安全查询使用情况</p>
            </div>
            <div className="bg-white/70 rounded-lg p-4">
              <div className="flex items-center justify-center mb-2">
                <BarChart3 className="w-5 h-5 text-blue-600" />
              </div>
              <h4 className="font-semibold text-gray-800 mb-1">实时数据</h4>
              <p className="text-gray-600">查看实时使用量和剩余额度</p>
            </div>
            <div className="bg-white/70 rounded-lg p-4">
              <div className="flex items-center justify-center mb-2">
                <Clock className="w-5 h-5 text-purple-600" />
              </div>
              <h4 className="font-semibold text-gray-800 mb-1">使用历史</h4>
              <p className="text-gray-600">查看详细的使用历史记录</p>
            </div>
          </div>
        </div>
      </div>

     
      {/* API Key Form Modal */}
      {showForm && (
        <ApiKeyForm
          apiKey={editingKey}
          onClose={() => {
            setShowForm(false);
            setEditingKey(null);
          }}
        />
      )}
    </div>
  );
};

export default ApiKeyManagement;