# 🤖 AI模型套餐平台

> 专业的AI模型API服务平台，提供OpenAI GPT-4、<PERSON>、DeepSeek、Gemini等主流AI模型套餐

[![React](https://img.shields.io/badge/React-18.x-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue.svg)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.x-38B2AC.svg)](https://tailwindcss.com/)
[![Vite](https://img.shields.io/badge/Vite-5.x-646CFF.svg)](https://vitejs.dev/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

## ✨ 平台特色

### 🎯 全模型支持
支持OpenAI、Claude、DeepSeek、Gemini、xAI等主流AI模型，一站式API服务

### 💰 价格优势
- **最低0.66折优惠**，让AI使用更经济实惠
- **1元起体验**，新手友好的入门套餐
- **灵活计费**，按需购买，支持体验到大额度套餐

### 🔧 客户端支持
完美支持多种主流AI客户端：
- 🌐 **沉浸式翻译插件** - 网页翻译必备
- ⭐ **Cherry Studio** - 推荐的AI对话工具
- 💬 **ChatBox** - 简洁易用的聊天界面
- 🎭 **SillyTavern** - 角色扮演专用客户端
- 📱 **更多客户端** - 持续扩展支持

## 🚀 核心功能

### 📦 套餐管理
- **套餐展示**: 清晰的价格表和套餐对比
- **实时优惠**: 限时优惠和推荐套餐标识
- **移动适配**: 桌面表格+移动卡片双重布局

### 📚 使用教程
- **API配置指南**: 详细的API接入教程
- **客户端配置**: 各种AI客户端的配置方法
- **推荐模型**: 模型选择和使用建议
- **联系购买**: 购买流程和联系方式

### 🤖 模型列表
- **供应商筛选**: 按OpenAI、Claude等供应商分类
- **模型搜索**: 快速查找特定模型
- **详细信息**: 模型ID、费率、推荐状态
- **批量下载**: 支持下载完整模型列表

### 🔑 API密钥管理
- **密钥管理**: 安全存储和管理API密钥
- **使用统计**: 实时查看API使用情况
- **余额查询**: 查看剩余额度和使用记录

## 🛠️ 技术架构

### 前端技术栈
- **⚛️ React 18** - 现代化的前端框架
- **📘 TypeScript** - 类型安全的JavaScript
- **🎨 Tailwind CSS** - 原子化CSS框架
- **⚡ Vite** - 极速构建工具
- **🎯 Lucide React** - 精美的图标库

### 核心特性
- **📱 响应式设计** - 移动优先，完美适配各种设备
- **🔍 SEO优化** - 完整的SEO配置和结构化数据
- **♿ 无障碍支持** - 符合WCAG标准的可访问性
- **⚡ 性能优化** - 代码分割、懒加载、资源优化
- **🛡️ 错误处理** - 全局错误边界和优雅降级

### 开发工具
- **🔧 SEO检查器** - 开发环境实时SEO检查
- **🎯 TypeScript** - 完整的类型定义
- **📊 性能监控** - Core Web Vitals监控
- **🔄 热重载** - 开发时实时更新

## 🚀 快速开始

### 环境要求
```bash
Node.js >= 18.0.0
npm >= 8.0.0 或 yarn >= 1.22.0
```

### 安装与运行
```bash
# 克隆项目
git clone https://github.com/your-username/ai-model-platform.git
cd ai-model-platform

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

### 开发环境特性
- 🔍 **SEO检查器**: 点击右下角眼睛图标查看实时SEO状态
- 🔄 **热重载**: 代码修改后自动刷新
- 📱 **移动端调试**: 支持移动设备调试

## 🌐 部署指南

### Cloudflare Pages (推荐)
```bash
# 1. 推送代码到GitHub
git push origin main

# 2. Cloudflare Pages配置
构建命令: npm run build
输出目录: dist
Node.js版本: 18.x

# 3. 环境变量 (可选)
NODE_ENV=production
```

### Vercel部署
```bash
# 安装Vercel CLI
npm i -g vercel

# 部署
vercel --prod
```

### Netlify部署
```bash
# 构建设置
Build command: npm run build
Publish directory: dist
```

### 自托管部署
```bash
# 构建项目
npm run build

# 部署到任何静态文件服务器
# 确保配置SPA路由重定向到index.html
```

## 📁 项目架构

```
📦 ai-model-platform/
├── 📂 src/
│   ├── 📂 components/           # React组件
│   │   ├── 📂 Layout/           # 布局组件
│   │   │   ├── Layout.tsx       # 主布局
│   │   │   └── TopNavigation.tsx # 顶部导航
│   │   ├── 📂 Packages/         # 套餐管理
│   │   │   └── PackageIntroduction.tsx
│   │   ├── 📂 Tutorial/         # 使用教程
│   │   │   └── Tutorial.tsx
│   │   ├── 📂 Models/           # 模型管理
│   │   │   └── ModelManagement.tsx
│   │   ├── 📂 ApiKeys/          # API密钥管理
│   │   │   ├── ApiKeyManagement.tsx
│   │   │   └── ApiKeyForm.tsx
│   │   ├── 📂 SEO/              # SEO组件
│   │   │   ├── SEOHead.tsx      # SEO头部
│   │   │   └── SEOChecker.tsx   # SEO检查器
│   │   └── ErrorBoundary.tsx    # 错误边界
│   ├── 📂 contexts/             # React Context
│   │   └── AppContext.tsx       # 全局状态管理
│   ├── 📂 hooks/                # 自定义Hooks
│   │   └── useSEO.ts           # SEO Hook
│   ├── 📂 config/               # 配置文件
│   │   └── seo.ts              # SEO配置
│   ├── 📂 utils/                # 工具函数
│   │   └── seoUtils.ts         # SEO工具
│   ├── 📂 types/                # TypeScript类型
│   ├── App.tsx                  # 应用根组件
│   ├── main.tsx                 # 应用入口
│   └── index.css                # 全局样式
├── 📂 public/                   # 静态资源
│   ├── sitemap.xml             # 网站地图
│   ├── robots.txt              # 爬虫配置
│   ├── _headers                # HTTP头配置
│   ├── _redirects              # 路由重定向
│   └── 📂 images/              # 图片资源
└── 📄 配置文件
    ├── package.json            # 项目配置
    ├── tsconfig.json           # TypeScript配置
    ├── tailwind.config.js      # Tailwind配置
    └── vite.config.ts          # Vite配置
```

## 🎯 核心特性详解

### 🔍 SEO优化系统
- **动态SEO**: 每个页面独立的SEO配置
- **结构化数据**: 完整的Schema.org标记
- **社交媒体**: Open Graph和Twitter卡片
- **实时检查**: 开发环境SEO检查器

### 📱 响应式设计
- **移动优先**: Mobile-first设计理念
- **断点系统**: sm(640px) / md(768px) / lg(1024px) / xl(1280px)
- **触摸优化**: 44px最小点击区域
- **自适应布局**: 桌面表格+移动卡片

### 🛡️ 错误处理
- **全局边界**: React Error Boundary
- **优雅降级**: 错误时显示友好界面
- **错误上报**: 开发环境错误详情
- **恢复机制**: 用户可重试操作

### ⚡ 性能优化
- **代码分割**: 路由级别的懒加载
- **资源优化**: 图片压缩和懒加载
- **缓存策略**: 静态资源长期缓存
- **Core Web Vitals**: 优化关键性能指标

## 🔧 配置指南

### SEO配置
```typescript
// src/config/seo.ts
export const siteInfo = {
  name: 'AI模型套餐平台',
  domain: 'your-domain.com', // 🚨 替换为实际域名
  logo: '/logo.png',         // 🚨 添加logo文件
  ogImage: '/og-image.png',  // 🚨 添加OG图片(1200x630px)
  // ...
};
```

### 环境变量
```bash
# .env.local
VITE_API_BASE_URL=https://api.your-domain.com
VITE_ANALYTICS_ID=your-analytics-id
```

### 部署前检查清单
- [ ] 更新域名配置 (`src/config/seo.ts`)
- [ ] 创建OG图片 (`public/og-image.png`)
- [ ] 创建Logo文件 (`public/logo.png`)
- [ ] 更新sitemap.xml域名
- [ ] 更新robots.txt域名
- [ ] 配置环境变量
- [ ] 测试SEO标签

## 📊 性能指标

### Core Web Vitals目标
- **LCP** (Largest Contentful Paint): < 2.5s
- **FID** (First Input Delay): < 100ms
- **CLS** (Cumulative Layout Shift): < 0.1

### SEO评分
- **技术SEO**: 95/100
- **内容SEO**: 85/100
- **移动友好**: 100/100
- **页面速度**: 90/100

## 🛠️ 开发工具

### 推荐VSCode扩展
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint"
  ]
}
```

### 调试工具
- **React DevTools**: 组件调试
- **SEO检查器**: 实时SEO检查 (开发环境)
- **Lighthouse**: 性能和SEO审计
- **Chrome DevTools**: 移动端调试

## 🤝 贡献指南

### 提交规范
```bash
# 功能开发
git commit -m "feat: 添加新功能"

# 问题修复
git commit -m "fix: 修复某个问题"

# 文档更新
git commit -m "docs: 更新文档"

# 样式调整
git commit -m "style: 调整样式"
```

### 开发流程
1. Fork项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'feat: 添加新功能'`)
4. 推送分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 📞 支持与联系

### 获取帮助
- 📖 **文档**: 查看项目Wiki和README
- 🐛 **问题反馈**: [GitHub Issues](https://github.com/your-username/ai-model-platform/issues)
- 💬 **讨论**: [GitHub Discussions](https://github.com/your-username/ai-model-platform/discussions)

### 商务合作
- 📧 **邮箱**: <EMAIL>
- 🌐 **官网**: https://your-domain.com
- 📱 **微信**: your-wechat-id

## 📄 许可证

本项目基于 [MIT License](LICENSE) 开源协议。

```
MIT License

Copyright (c) 2025 AI模型套餐平台

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.
```

## 🌟 致谢

感谢所有为这个项目做出贡献的开发者和用户！

### 技术栈致谢
- [React](https://reactjs.org/) - 用户界面库
- [TypeScript](https://www.typescriptlang.org/) - 类型安全
- [Tailwind CSS](https://tailwindcss.com/) - CSS框架
- [Vite](https://vitejs.dev/) - 构建工具
- [Lucide](https://lucide.dev/) - 图标库

---

<div align="center">

**🚀 让AI触手可及，让创新无处不在 🚀**

[官网](https://your-domain.com) • [文档](https://docs.your-domain.com) • [演示](https://demo.your-domain.com)

</div>


