import React from 'react';
import {
  BookOpen,
  Bot,
  BarChart3,
  ChevronRight,
  Zap,
  ShoppingCart,
  Grid3X3
} from 'lucide-react';
import { useApp } from '../../contexts/AppContext';

const menuItems = [
  { id: 'packages', label: '套餐介绍', icon: ShoppingCart },
  { id: 'tutorial', label: '使用帮助', icon: BookOpen },
  { id: 'models', label: '模型列表', icon: Bot },
  { id: 'more-models', label: '更多模型', icon: Grid3X3 },
  { id: 'api-keys', label: 'API使用查询', icon: BarChart3 },
];

const Sidebar: React.FC = () => {
  const { state, dispatch } = useApp();

  return (
    <div className="w-64 bg-gray-900 min-h-screen flex flex-col">
      <div className="p-6 border-b border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
            <Zap className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">模型中心</h1>
            <p className="text-sm text-gray-400">AI管理平台</p>
          </div>
        </div>
      </div>

      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = state.currentView === item.id;
            
            return (
              <li key={item.id}>
                <button
                  onClick={() => dispatch({ type: 'SET_CURRENT_VIEW', payload: item.id })}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 group ${
                    isActive
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium">{item.label}</span>
                  {isActive && (
                    <ChevronRight className="w-4 h-4 ml-auto" />
                  )}
                </button>
              </li>
            );
          })}
        </ul>
      </nav>
    </div>
  );
};

export default Sidebar;