import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { Model, ApiKey, Usage, User } from '../types';

interface AppState {
  user: User | null;
  models: Model[];
  apiKeys: Api<PERSON>ey[];
  usage: Usage[];
  currentView: string;
  tutorialSection: string;
  tutorialClient: string;
  selectedProvider: string;
}

type AppAction =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_MODELS'; payload: Model[] }
  | { type: 'ADD_MODEL'; payload: Model }
  | { type: 'UPDATE_MODEL'; payload: Model }
  | { type: 'DELETE_MODEL'; payload: string }
  | { type: 'SET_API_KEYS'; payload: ApiKey[] }
  | { type: 'ADD_API_KEY'; payload: ApiKey }
  | { type: 'UPDATE_API_KEY'; payload: ApiKey }
  | { type: 'DELETE_API_KEY'; payload: string }
  | { type: 'SET_USAGE'; payload: Usage[] }
  | { type: 'ADD_USAGE'; payload: Usage }
  | { type: 'SET_CURRENT_VIEW'; payload: string }
  | { type: 'SET_TUTORIAL_SECTION'; payload: string }
  | { type: 'SET_TUTORIAL_CLIENT'; payload: string }
  | { type: 'SET_SELECTED_PROVIDER'; payload: string };

const initialState: AppState = {
  user: {
    id: '1',
    name: '管理员用户',
    email: '<EMAIL>',
    role: 'admin',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
  },
  models: [
    {
      id: '1',
      name: 'GPT-4 Turbo',
      provider: 'OpenAI',
      type: 'text',
      status: 'active',
      apiEndpoint: 'https://api.openai.com/v1/chat/completions',
      maxTokens: 4096,
      costPerToken: 0.00003,
      description: '最强大的GPT-4模型，具有更高的效率',
      createdAt: '2024-01-15T10:00:00Z',
      lastUsed: '2024-01-20T14:30:00Z',
      totalRequests: 15420,
      successRate: 99.2
    },
    {
      id: '2',
      name: 'Claude-3 Sonnet',
      provider: 'Anthropic',
      type: 'text',
      status: 'active',
      apiEndpoint: 'https://api.anthropic.com/v1/messages',
      maxTokens: 4096,
      costPerToken: 0.000015,
      description: '适用于通用任务的平衡模型',
      createdAt: '2024-01-10T09:00:00Z',
      lastUsed: '2024-01-20T16:45:00Z',
      totalRequests: 8930,
      successRate: 98.8
    },
    {
      id: '3',
      name: 'DALL-E 3',
      provider: 'OpenAI',
      type: 'image',
      status: 'active',
      apiEndpoint: 'https://api.openai.com/v1/images/generations',
      maxTokens: 1024,
      costPerToken: 0.04,
      description: '先进的图像生成模型',
      createdAt: '2024-01-12T11:00:00Z',
      lastUsed: '2024-01-19T20:15:00Z',
      totalRequests: 2450,
      successRate: 97.5
    }
  ],
  apiKeys: [
    {
      id: '1',
      name: '生产环境密钥',
      key: 'sk-proj-***************',
      modelIds: ['1', '2'],
      permissions: ['read', 'write'],
      status: 'active',
      createdAt: '2024-01-15T10:00:00Z',
      lastUsed: '2024-01-20T14:30:00Z',
      usageCount: 12340,
      rateLimit: 1000
    },
    {
      id: '2',
      name: '开发环境密钥',
      key: 'sk-test-***************',
      modelIds: ['1', '2', '3'],
      permissions: ['read'],
      status: 'active',
      createdAt: '2024-01-10T09:00:00Z',
      lastUsed: '2024-01-20T12:15:00Z',
      usageCount: 4560,
      rateLimit: 100
    }
  ],
  usage: [],
  currentView: 'packages',
  tutorialSection: 'api',
  tutorialClient: 'immersive-translate',
  selectedProvider: 'OpenAI'
};

const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload };
    case 'SET_MODELS':
      return { ...state, models: action.payload };
    case 'ADD_MODEL':
      return { ...state, models: [...state.models, action.payload] };
    case 'UPDATE_MODEL':
      return {
        ...state,
        models: state.models.map(model =>
          model.id === action.payload.id ? action.payload : model
        )
      };
    case 'DELETE_MODEL':
      return {
        ...state,
        models: state.models.filter(model => model.id !== action.payload)
      };
    case 'SET_API_KEYS':
      return { ...state, apiKeys: action.payload };
    case 'ADD_API_KEY':
      return { ...state, apiKeys: [...state.apiKeys, action.payload] };
    case 'UPDATE_API_KEY':
      return {
        ...state,
        apiKeys: state.apiKeys.map(key =>
          key.id === action.payload.id ? action.payload : key
        )
      };
    case 'DELETE_API_KEY':
      return {
        ...state,
        apiKeys: state.apiKeys.filter(key => key.id !== action.payload)
      };
    case 'SET_USAGE':
      return { ...state, usage: action.payload };
    case 'ADD_USAGE':
      return { ...state, usage: [action.payload, ...state.usage] };
    case 'SET_CURRENT_VIEW':
      return { ...state, currentView: action.payload };
    case 'SET_TUTORIAL_SECTION':
      return { ...state, tutorialSection: action.payload };
    case 'SET_TUTORIAL_CLIENT':
      return { ...state, tutorialClient: action.payload };
    case 'SET_SELECTED_PROVIDER':
      return { ...state, selectedProvider: action.payload };
    default:
      return state;
  }
};

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
};

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};