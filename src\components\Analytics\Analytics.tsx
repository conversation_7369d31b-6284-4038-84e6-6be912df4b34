import React from 'react';
import { TrendingUp, TrendingDown, DollarSign, Clock, Users, Zap } from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import StatsCard from '../Dashboard/StatsCard';

const Analytics: React.FC = () => {
  const { state } = useApp();

  // Calculate analytics data
  const totalCost = state.models.reduce((sum, model) => 
    sum + (model.totalRequests * model.costPerToken), 0
  );
  
  const avgResponseTime = 245; // Mock data
  const totalUsers = 156; // Mock data
  const uptime = 99.9; // Mock data

  const usageByModel = state.models.map(model => ({
    name: model.name,
    requests: model.totalRequests,
    cost: model.totalRequests * model.costPerToken,
    successRate: model.successRate
  }));

  const monthlyData = [
    { month: '1月', requests: 12000, cost: 450 },
    { month: '2月', requests: 15000, cost: 560 },
    { month: '3月', requests: 18000, cost: 670 },
    { month: '4月', requests: 22000, cost: 820 },
    { month: '5月', requests: 25000, cost: 930 },
    { month: '6月', requests: 28000, cost: 1040 }
  ];

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="总成本"
          value={`$${totalCost.toFixed(2)}`}
          change="较上月 +8.2%"
          icon={DollarSign}
          color="green"
        />
        <StatsCard
          title="平均响应时间"
          value={`${avgResponseTime}ms`}
          change="较上周 -15ms"
          icon={Clock}
          color="blue"
        />
        <StatsCard
          title="活跃用户"
          value={totalUsers}
          change="本月新增 +23"
          icon={Users}
          color="purple"
        />
        <StatsCard
          title="系统正常运行时间"
          value={`${uptime}%`}
          change="所有系统正常运行"
          icon={Zap}
          color="yellow"
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Usage Chart */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">月度使用趋势</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {monthlyData.map((item, index) => {
                const maxRequests = Math.max(...monthlyData.map(d => d.requests));
                const maxCost = Math.max(...monthlyData.map(d => d.cost));
                
                return (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">{item.month}</span>
                      <div className="space-x-4">
                        <span className="text-blue-600">{item.requests.toLocaleString()} 请求</span>
                        <span className="text-green-600">${item.cost}</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <div 
                        className="h-2 bg-blue-500 rounded-full"
                        style={{ width: `${(item.requests / maxRequests) * 100}%` }}
                      />
                      <div 
                        className="h-2 bg-green-500 rounded-full"
                        style={{ width: `${(item.cost / maxCost) * 100}%` }}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Model Performance */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">模型性能</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {usageByModel.map((model, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-gray-900">{model.name}</h4>
                    <span className="text-sm text-gray-500">{model.successRate}% 成功率</span>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">请求数:</span>
                      <span className="ml-2 font-medium">{model.requests.toLocaleString()}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">成本:</span>
                      <span className="ml-2 font-medium">${model.cost.toFixed(2)}</span>
                    </div>
                  </div>
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full"
                        style={{ width: `${model.successRate}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Error Analysis */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">错误分析</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <TrendingDown className="w-8 h-8 text-red-600" />
              </div>
              <h4 className="font-semibold text-gray-900">速率限制错误</h4>
              <p className="text-2xl font-bold text-red-600 mt-1">12</p>
              <p className="text-sm text-gray-500">过去24小时</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Clock className="w-8 h-8 text-yellow-600" />
              </div>
              <h4 className="font-semibold text-gray-900">超时错误</h4>
              <p className="text-2xl font-bold text-yellow-600 mt-1">5</p>
              <p className="text-sm text-gray-500">过去24小时</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="w-8 h-8 text-blue-600" />
              </div>
              <h4 className="font-semibold text-gray-900">成功率</h4>
              <p className="text-2xl font-bold text-blue-600 mt-1">98.5%</p>
              <p className="text-sm text-gray-500">总体</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;