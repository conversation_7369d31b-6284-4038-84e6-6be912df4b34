import React from 'react';
import { Bot, Key, Activity, DollarSign, TrendingUp, AlertTriangle } from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import StatsCard from './StatsCard';
import RecentActivity from './RecentActivity';
import UsageChart from './UsageChart';

const Dashboard: React.FC = () => {
  const { state } = useApp();

  const totalModels = state.models.length;
  const activeModels = state.models.filter(m => m.status === 'active').length;
  const totalApiKeys = state.apiKeys.length;
  const activeApiKeys = state.apiKeys.filter(k => k.status === 'active').length;
  const totalRequests = state.models.reduce((sum, model) => sum + model.totalRequests, 0);
  const avgSuccessRate = state.models.reduce((sum, model) => sum + model.successRate, 0) / state.models.length;

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="模型总数"
          value={totalModels}
          change={`${activeModels} 个活跃`}
          icon={Bot}
          color="blue"
        />
        <StatsCard
          title="API密钥"
          value={totalApiKeys}
          change={`${activeApiKeys} 个活跃`}
          icon={Key}
          color="green"
        />
        <StatsCard
          title="总请求数"
          value={totalRequests.toLocaleString()}
          change="较上月 +12%"
          icon={Activity}
          color="purple"
        />
        <StatsCard
          title="成功率"
          value={`${avgSuccessRate.toFixed(1)}%`}
          change="较上周 +0.3%"
          icon={TrendingUp}
          color="yellow"
        />
      </div>

      {/* Charts and Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <UsageChart />
        </div>
        <div>
          <RecentActivity />
        </div>
      </div>

      {/* Model Status Grid */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">模型状态概览</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {state.models.map((model) => (
              <div key={model.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-gray-900">{model.name}</h4>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    model.status === 'active' 
                      ? 'bg-green-100 text-green-800' 
                      : model.status === 'maintenance'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {model.status === 'active' ? '活跃' : model.status === 'maintenance' ? '维护中' : '停用'}
                  </span>
                </div>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>提供商:</span>
                    <span className="font-medium">{model.provider}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>请求数:</span>
                    <span className="font-medium">{model.totalRequests.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>成功率:</span>
                    <span className="font-medium">{model.successRate}%</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;