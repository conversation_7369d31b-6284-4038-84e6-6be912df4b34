# SEO 配置说明

本项目已经完整配置了SEO优化，包括基础SEO标签、Open Graph、Twitter Cards、结构化数据等。

## 📋 已实现的SEO功能

### 1. 基础SEO标签
- ✅ 页面标题 (Title)
- ✅ 页面描述 (Description)
- ✅ 关键词 (Keywords)
- ✅ 作者信息 (Author)
- ✅ 语言设置 (Language)
- ✅ Canonical URL

### 2. Open Graph 标签
- ✅ og:title
- ✅ og:description
- ✅ og:type
- ✅ og:url
- ✅ og:image
- ✅ og:site_name
- ✅ og:locale

### 3. Twitter Cards
- ✅ twitter:card
- ✅ twitter:title
- ✅ twitter:description
- ✅ twitter:image
- ✅ twitter:url

### 4. 结构化数据 (JSON-LD)
- ✅ WebSite 结构化数据
- ✅ Product 结构化数据
- ✅ Service 结构化数据
- ✅ HowTo 结构化数据
- ✅ ItemList 结构化数据

### 5. 技术SEO
- ✅ Sitemap.xml
- ✅ Robots.txt
- ✅ 移动端适配
- ✅ 页面加载优化

## 🔧 配置文件说明

### SEO配置文件
- `src/config/seo.ts` - SEO配置中心
- `src/hooks/useSEO.ts` - SEO Hook
- `src/components/SEO/SEOHead.tsx` - SEO组件
- `src/utils/seoUtils.ts` - SEO工具函数

### 页面SEO配置
每个页面都有独立的SEO配置：
- **首页/套餐页面**: 重点优化AI模型套餐相关关键词
- **教程页面**: 优化使用教程、配置指南相关关键词
- **模型页面**: 优化模型列表、模型ID相关关键词
- **API密钥页面**: 优化API管理相关关键词

## 🚀 使用方法

### 1. 在组件中使用SEO
```tsx
import { useSEO } from '../../hooks/useSEO';

const MyComponent = () => {
  // 使用预定义的页面SEO配置
  useSEO('packages');
  
  // 或者使用自定义配置
  useSEO('packages', {
    title: '自定义标题',
    description: '自定义描述'
  });
  
  return <div>...</div>;
};
```

### 2. 更新域名配置
在 `src/config/seo.ts` 中更新您的实际域名：
```typescript
export const siteInfo = {
  name: 'AI模型套餐平台',
  domain: 'your-actual-domain.com', // 替换为实际域名
  // ...
};
```

### 3. 更新sitemap.xml
在 `public/sitemap.xml` 中更新域名：
```xml
<loc>https://your-actual-domain.com/</loc>
```

### 4. 更新robots.txt
在 `public/robots.txt` 中更新sitemap地址：
```
Sitemap: https://your-actual-domain.com/sitemap.xml
```

## 📊 SEO检查工具

项目包含了SEO检查工具函数：

```typescript
import { 
  validateSEOTags, 
  getPageSEOInfo, 
  getSEOSuggestions,
  checkImageSEO,
  checkLinkSEO
} from '../utils/seoUtils';

// 验证SEO标签
const seoValidation = validateSEOTags();

// 获取页面SEO信息
const seoInfo = getPageSEOInfo();

// 获取SEO建议
const suggestions = getSEOSuggestions();
```

## 🎯 关键词策略

### 主要关键词
- AI模型
- OpenAI
- GPT-4
- Claude
- DeepSeek
- API服务
- AI套餐

### 长尾关键词
- 沉浸式翻译插件配置
- Cherry Studio使用教程
- AI模型API价格
- OpenAI API密钥
- Claude API使用方法

## 📈 SEO最佳实践

### 1. 标题优化
- 长度控制在30-60个字符
- 包含主要关键词
- 每个页面标题唯一

### 2. 描述优化
- 长度控制在120-160个字符
- 包含关键词和行动号召
- 准确描述页面内容

### 3. 关键词优化
- 每页3-10个相关关键词
- 避免关键词堆砌
- 使用语义相关的词汇

### 4. 图片优化
- 所有图片都有alt属性
- 文件名包含关键词
- 图片大小优化

### 5. 链接优化
- 内部链接使用描述性锚文本
- 外部链接添加noopener noreferrer
- 建立合理的链接结构

## 🔍 监控和分析

建议使用以下工具监控SEO效果：
- Google Search Console
- Google Analytics
- 百度站长工具
- 站长之家SEO查询

## 📝 注意事项

1. **域名替换**: 记得将所有配置文件中的 `your-domain.com` 替换为实际域名
2. **图片路径**: 确保所有引用的图片文件存在于public目录
3. **定期更新**: 根据内容变化定期更新SEO配置
4. **性能优化**: 保持页面加载速度，这对SEO很重要

## 🆕 后续优化建议

1. 添加面包屑导航
2. 实现AMP页面
3. 添加FAQ结构化数据
4. 优化Core Web Vitals
5. 实现多语言SEO
