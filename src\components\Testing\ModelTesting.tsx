import React, { useState } from 'react';
import { Play, Send, Copy, Download, Settings } from 'lucide-react';
import { useApp } from '../../contexts/AppContext';

interface TestResult {
  id: string;
  timestamp: string;
  modelId: string;
  input: string;
  output: string;
  responseTime: number;
  status: 'success' | 'error';
  error?: string;
}

const ModelTesting: React.FC = () => {
  const { state } = useApp();
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [selectedApiKey, setSelectedApiKey] = useState<string>('');
  const [testInput, setTestInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [testConfig, setTestConfig] = useState({
    maxTokens: 1000,
    temperature: 0.7,
    topP: 1,
    frequencyPenalty: 0,
    presencePenalty: 0
  });

  const activeModels = state.models.filter(model => model.status === 'active');
  const activeApiKeys = state.apiKeys.filter(key => key.status === 'active');

  const handleTest = async () => {
    if (!selectedModel || !selectedApiKey || !testInput.trim()) {
      alert('请选择模型、API密钥并输入测试内容');
      return;
    }

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const model = state.models.find(m => m.id === selectedModel);
      const result: TestResult = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        modelId: selectedModel,
        input: testInput,
        output: `这是来自 ${model?.name} 的模拟响应。模型会处理您的输入："${testInput}"，并根据其训练和能力提供相关响应。`,
        responseTime: Math.floor(Math.random() * 2000) + 200,
        status: Math.random() > 0.1 ? 'success' : 'error',
        error: Math.random() > 0.1 ? undefined : '超出速率限制'
      };

      setTestResults(prev => [result, ...prev]);
      setIsLoading(false);
    }, 1000 + Math.random() * 2000);
  };

  const copyResult = (result: TestResult) => {
    const text = `模型: ${state.models.find(m => m.id === result.modelId)?.name}
输入: ${result.input}
输出: ${result.output}
响应时间: ${result.responseTime}ms
状态: ${result.status}`;
    
    navigator.clipboard.writeText(text);
  };

  const exportResults = () => {
    const csv = [
      '时间戳,模型,输入,输出,响应时间,状态,错误',
      ...testResults.map(result => {
        const model = state.models.find(m => m.id === result.modelId)?.name || '未知';
        return `"${result.timestamp}","${model}","${result.input}","${result.output}",${result.responseTime},"${result.status}","${result.error || ''}"`;
      })
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'model-test-results.csv';
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Test Configuration */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">模型测试界面</h3>
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center space-x-2 text-blue-600 hover:text-blue-700"
            >
              <Settings className="w-5 h-5" />
              <span>{showAdvanced ? '隐藏' : '显示'}高级选项</span>
            </button>
          </div>
        </div>
        
        <div className="p-6 space-y-6">
          {/* Basic Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择模型
              </label>
              <select
                value={selectedModel}
                onChange={(e) => setSelectedModel(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">选择一个模型...</option>
                {activeModels.map((model) => (
                  <option key={model.id} value={model.id}>
                    {model.name} ({model.provider})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择API密钥
              </label>
              <select
                value={selectedApiKey}
                onChange={(e) => setSelectedApiKey(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">选择一个API密钥...</option>
                {activeApiKeys.map((key) => (
                  <option key={key.id} value={key.id}>
                    {key.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Advanced Configuration */}
          {showAdvanced && (
            <div className="border-t border-gray-200 pt-6">
              <h4 className="text-md font-medium text-gray-900 mb-4">高级参数</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    最大令牌数
                  </label>
                  <input
                    type="number"
                    value={testConfig.maxTokens}
                    onChange={(e) => setTestConfig({...testConfig, maxTokens: parseInt(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    温度
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    max="2"
                    value={testConfig.temperature}
                    onChange={(e) => setTestConfig({...testConfig, temperature: parseFloat(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Top P
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    max="1"
                    value={testConfig.topP}
                    onChange={(e) => setTestConfig({...testConfig, topP: parseFloat(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Test Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              测试输入
            </label>
            <textarea
              value={testInput}
              onChange={(e) => setTestInput(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="在此输入您的测试提示或内容..."
            />
          </div>

          {/* Test Button */}
          <div className="flex items-center justify-between">
            <button
              onClick={handleTest}
              disabled={isLoading || !selectedModel || !selectedApiKey || !testInput.trim()}
              className="flex items-center space-x-2 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>测试中...</span>
                </>
              ) : (
                <>
                  <Play className="w-5 h-5" />
                  <span>运行测试</span>
                </>
              )}
            </button>

            {testResults.length > 0 && (
              <button
                onClick={exportResults}
                className="flex items-center space-x-2 text-gray-600 border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Download className="w-5 h-5" />
                <span>导出结果</span>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">测试结果</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {testResults.map((result) => {
              const model = state.models.find(m => m.id === result.modelId);
              return (
                <div key={result.id} className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="font-semibold text-gray-900">{model?.name}</h4>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          result.status === 'success' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {result.status === 'success' ? '成功' : '失败'}
                        </span>
                        <span className="text-sm text-gray-500">
                          {result.responseTime}ms
                        </span>
                      </div>
                      <p className="text-sm text-gray-500">
                        {new Date(result.timestamp).toLocaleString()}
                      </p>
                    </div>
                    <button
                      onClick={() => copyResult(result)}
                      className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                    >
                      <Copy className="w-5 h-5" />
                    </button>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">输入:</h5>
                      <div className="bg-gray-50 rounded-lg p-3 text-sm text-gray-900">
                        {result.input}
                      </div>
                    </div>

                    {result.status === 'success' ? (
                      <div>
                        <h5 className="text-sm font-medium text-gray-700 mb-2">输出:</h5>
                        <div className="bg-blue-50 rounded-lg p-3 text-sm text-gray-900">
                          {result.output}
                        </div>
                      </div>
                    ) : (
                      <div>
                        <h5 className="text-sm font-medium text-gray-700 mb-2">错误:</h5>
                        <div className="bg-red-50 rounded-lg p-3 text-sm text-red-700">
                          {result.error}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default ModelTesting;