import React, { useState } from 'react';
import { 
  Key, 
  Book, 
  Shield, 
  Globe, 
  Bell, 
  Palette, 
  Database, 
  Download,
  Copy,
  CheckCircle,
  AlertTriangle,
  Info,
  ExternalLink,
  Settings as SettingsIcon
} from 'lucide-react';
import { useApp } from '../../contexts/AppContext';

const Settings: React.FC = () => {
  const { state } = useApp();
  const [activeTab, setActiveTab] = useState('api-help');

  const tabs = [
    { id: 'api-help', label: 'API密钥使用帮助', icon: Key },
    { id: 'documentation', label: '文档', icon: Book },
    { id: 'security', label: '安全设置', icon: Shield },
    { id: 'general', label: '常规设置', icon: SettingsIcon },
    { id: 'notifications', label: '通知', icon: Bell },
    { id: 'appearance', label: '外观', icon: Palette },
    { id: 'data', label: '数据管理', icon: Database }
  ];

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const renderApiHelp = () => (
    <div className="space-y-8">
      {/* Quick Start Guide */}
      <section>
        <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
          <CheckCircle className="w-6 h-6 text-green-500 mr-2" />
          快速开始指南
        </h3>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <ol className="space-y-4 text-sm">
            <li className="flex items-start">
              <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</span>
              <div>
                <strong>生成API密钥：</strong> 导航到API密钥部分，点击"生成API密钥"
              </div>
            </li>
            <li className="flex items-start">
              <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</span>
              <div>
                <strong>配置权限：</strong> 为您的使用场景选择适当的模型和权限
              </div>
            </li>
            <li className="flex items-start">
              <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</span>
              <div>
                <strong>设置速率限制：</strong> 配置速率限制以控制使用量和成本
              </div>
            </li>
            <li className="flex items-start">
              <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">4</span>
              <div>
                <strong>开始发送请求：</strong> 在HTTP头中使用您的API密钥进行身份验证
              </div>
            </li>
          </ol>
        </div>
      </section>

      {/* Authentication */}
      <section>
        <h3 className="text-xl font-semibold text-gray-900 mb-4">身份验证</h3>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <p className="text-gray-700 mb-4">
            在HTTP请求的Authorization头中包含您的API密钥：
          </p>
          <div className="bg-gray-900 text-green-400 rounded-lg p-4 font-mono text-sm relative">
            <div className="flex justify-between items-start">
              <pre className="whitespace-pre-wrap">
                {`curl -X POST https://api.example.com/v1/chat/completions \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "gpt-4-turbo",
    "messages": [{"role": "user", "content": "你好！"}]
  }'`}
              </pre>
              <button
                onClick={() => copyToClipboard(`curl -X POST https://api.example.com/v1/chat/completions \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "gpt-4-turbo",
    "messages": [{"role": "user", "content": "你好！"}]
  }'`)}
                className="text-gray-400 hover:text-white"
              >
                <Copy className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Rate Limits */}
      <section>
        <h3 className="text-xl font-semibold text-gray-900 mb-4">速率限制和使用</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <div className="flex items-center mb-3">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2" />
              <h4 className="font-semibold text-yellow-800">速率限制</h4>
            </div>
            <ul className="text-sm text-yellow-700 space-y-2">
              <li>• 每个API密钥都有配置的速率限制（每分钟请求数）</li>
              <li>• 超出限制会返回HTTP 429状态码</li>
              <li>• 使用指数退避进行重试逻辑</li>
              <li>• 在分析部分监控使用情况</li>
            </ul>
          </div>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-center mb-3">
              <Info className="w-5 h-5 text-green-600 mr-2" />
              <h4 className="font-semibold text-green-800">最佳实践</h4>
            </div>
            <ul className="text-sm text-green-700 space-y-2">
              <li>• 实施适当的错误处理</li>
              <li>• 在适当时缓存响应</li>
              <li>• 使用批量请求提高效率</li>
              <li>• 定期监控成本和使用情况</li>
            </ul>
          </div>
        </div>
      </section>

      {/* API Examples */}
      <section>
        <h3 className="text-xl font-semibold text-gray-900 mb-4">API示例</h3>
        
        <div className="space-y-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="font-semibold text-gray-900 mb-3">文本生成 (OpenAI格式)</h4>
            <div className="bg-gray-900 text-green-400 rounded-lg p-4 font-mono text-sm relative">
              <button
                onClick={() => copyToClipboard(`{
  "model": "gpt-4-turbo",
  "messages": [
    {"role": "system", "content": "你是一个有用的助手。"},
    {"role": "user", "content": "解释量子计算"}
  ],
  "max_tokens": 500,
  "temperature": 0.7
}`)}
                className="absolute top-2 right-2 text-gray-400 hover:text-white"
              >
                <Copy className="w-4 h-4" />
              </button>
              <pre>
                {`{
  "model": "gpt-4-turbo",
  "messages": [
    {"role": "system", "content": "你是一个有用的助手。"},
    {"role": "user", "content": "解释量子计算"}
  ],
  "max_tokens": 500,
  "temperature": 0.7
}`}
              </pre>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="font-semibold text-gray-900 mb-3">图像生成</h4>
            <div className="bg-gray-900 text-green-400 rounded-lg p-4 font-mono text-sm relative">
              <button
                onClick={() => copyToClipboard(`{
  "model": "dall-e-3",
  "prompt": "日落时分的未来城市天际线",
  "size": "1024x1024",
  "quality": "standard",
  "n": 1
}`)}
                className="absolute top-2 right-2 text-gray-400 hover:text-white"
              >
                <Copy className="w-4 h-4" />
              </button>
              <pre>
                {`{
  "model": "dall-e-3",
  "prompt": "日落时分的未来城市天际线",
  "size": "1024x1024",
  "quality": "standard",
  "n": 1
}`}
              </pre>
            </div>
          </div>
        </div>
      </section>

      {/* Error Handling */}
      <section>
        <h3 className="text-xl font-semibold text-gray-900 mb-4">错误处理</h3>
        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">状态码</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">描述</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">建议操作</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              <tr>
                <td className="px-6 py-4 text-sm font-mono text-red-600">401</td>
                <td className="px-6 py-4 text-sm text-gray-900">未授权 - 无效的API密钥</td>
                <td className="px-6 py-4 text-sm text-gray-600">检查API密钥的有效性和权限</td>
              </tr>
              <tr>
                <td className="px-6 py-4 text-sm font-mono text-yellow-600">429</td>
                <td className="px-6 py-4 text-sm text-gray-900">超出速率限制</td>
                <td className="px-6 py-4 text-sm text-gray-600">实施指数退避</td>
              </tr>
              <tr>
                <td className="px-6 py-4 text-sm font-mono text-red-600">500</td>
                <td className="px-6 py-4 text-sm text-gray-900">服务器错误</td>
                <td className="px-6 py-4 text-sm text-gray-600">延迟后重试</td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>
    </div>
  );

  const renderDocumentation = () => (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
            <Book className="w-6 h-6 text-blue-600" />
          </div>
          <h3 className="font-semibold text-gray-900 mb-2">API参考</h3>
          <p className="text-gray-600 text-sm mb-4">包含示例和响应格式的完整API文档</p>
          <a href="#" className="text-blue-600 text-sm font-medium flex items-center hover:text-blue-700">
            查看文档
            <ExternalLink className="w-4 h-4 ml-1" />
          </a>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
          <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
            <Globe className="w-6 h-6 text-green-600" />
          </div>
          <h3 className="font-semibold text-gray-900 mb-2">SDK和库</h3>
          <p className="text-gray-600 text-sm mb-4">Python、JavaScript、Go等官方SDK</p>
          <a href="#" className="text-blue-600 text-sm font-medium flex items-center hover:text-blue-700">
            浏览SDK
            <ExternalLink className="w-4 h-4 ml-1" />
          </a>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
          <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
            <Shield className="w-6 h-6 text-purple-600" />
          </div>
          <h3 className="font-semibold text-gray-900 mb-2">安全指南</h3>
          <p className="text-gray-600 text-sm mb-4">安全API密钥管理和使用的最佳实践</p>
          <a href="#" className="text-blue-600 text-sm font-medium flex items-center hover:text-blue-700">
            安全指南
            <ExternalLink className="w-4 h-4 ml-1" />
          </a>
        </div>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">入门教程</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">构建您的第一个AI应用</h4>
              <p className="text-sm text-gray-600">学习如何将我们的API集成到您的应用中</p>
            </div>
            <a href="#" className="text-blue-600 text-sm font-medium hover:text-blue-700">开始教程</a>
          </div>
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">高级模型配置</h4>
              <p className="text-sm text-gray-600">为您的特定用例优化模型参数</p>
            </div>
            <a href="#" className="text-blue-600 text-sm font-medium hover:text-blue-700">开始教程</a>
          </div>
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">监控和分析</h4>
              <p className="text-sm text-gray-600">跟踪使用情况、成本和性能指标</p>
            </div>
            <a href="#" className="text-blue-600 text-sm font-medium hover:text-blue-700">开始教程</a>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'api-help':
        return renderApiHelp();
      case 'documentation':
        return renderDocumentation();
      case 'security':
        return (
          <div className="space-y-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-red-800 mb-2">安全建议</h3>
              <ul className="text-red-700 space-y-2">
                <li>• 永远不要在客户端代码中暴露API密钥</li>
                <li>• 使用环境变量存储密钥</li>
                <li>• 定期轮换您的API密钥</li>
                <li>• 设置适当的过期日期</li>
                <li>• 监控使用情况以发现异常模式</li>
              </ul>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">双因素认证</h3>
              <p className="text-gray-600 mb-4">启用2FA以增强账户安全性</p>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                启用2FA
              </button>
            </div>
          </div>
        );
      case 'general':
        return (
          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">个人资料设置</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">组织名称</label>
                  <input type="text" defaultValue="模型中心公司" className="w-full px-3 py-2 border border-gray-300 rounded-lg" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">默认模型</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    <option>GPT-4 Turbo</option>
                    <option>Claude-3 Sonnet</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        );
      case 'notifications':
        return (
          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">通知偏好</h3>
              <div className="space-y-4">
                <label className="flex items-center">
                  <input type="checkbox" defaultChecked className="rounded border-gray-300 text-blue-600" />
                  <span className="ml-2 text-gray-700">速率限制警告</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" defaultChecked className="rounded border-gray-300 text-blue-600" />
                  <span className="ml-2 text-gray-700">API密钥过期提醒</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="rounded border-gray-300 text-blue-600" />
                  <span className="ml-2 text-gray-700">每周使用报告</span>
                </label>
              </div>
            </div>
          </div>
        );
      case 'appearance':
        return (
          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">主题设置</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">配色方案</label>
                  <div className="flex space-x-4">
                    <button className="w-12 h-12 bg-blue-500 rounded-lg border-2 border-blue-600"></button>
                    <button className="w-12 h-12 bg-green-500 rounded-lg border-2 border-transparent hover:border-green-600"></button>
                    <button className="w-12 h-12 bg-purple-500 rounded-lg border-2 border-transparent hover:border-purple-600"></button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case 'data':
        return (
          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">数据导出</h3>
              <div className="space-y-4">
                <button className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                  <Download className="w-5 h-5" />
                  <span>导出使用数据</span>
                </button>
                <button className="flex items-center space-x-2 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                  <Download className="w-5 h-5" />
                  <span>导出API密钥</span>
                </button>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex h-full">
      {/* Sidebar */}
      <div className="w-64 bg-white border-r border-gray-200 flex-shrink-0">
        <div className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">设置</h2>
          <nav className="space-y-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-8 overflow-y-auto">
        <div className="max-w-4xl">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default Settings;