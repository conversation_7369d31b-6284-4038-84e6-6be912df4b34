import React from 'react';
import { useApp } from '../../contexts/AppContext';

const Header: React.FC = () => {
  const { state } = useApp();
  
  const getViewTitle = (view: string) => {
    const titles: Record<string, string> = {
      packages: '套餐介绍',
      tutorial: '操作教程',
      models: '模型管理',
      'api-keys': 'API使用查询'
    };
    return titles[view] || '套餐介绍';
  };

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {getViewTitle(state.currentView)}
          </h1>
          <p className="text-sm text-gray-600 mt-1">
            管理您的AI模型和API集成
          </p>
        </div>
      </div>
    </header>
  );
};

export default Header;