# 部署指南

本项目支持多种部署方式，推荐使用 Cloudflare Pages 进行部署。

## 🚀 Cloudflare Pages 部署

### 1. 自动部署（推荐）

1. **连接 GitHub 仓库**：
   - 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
   - 进入 Pages 页面
   - 点击 "Create a project"
   - 选择 "Connect to Git"
   - 选择您的 GitHub 仓库：`gaoweibiger/APIKey`

2. **配置构建设置**：
   ```
   Framework preset: Vite
   Build command: npm run build
   Build output directory: dist
   Root directory: (留空)
   ```

3. **环境变量**（可选）：
   ```
   NODE_ENV=production
   ```

4. **部署**：
   - 点击 "Save and Deploy"
   - 等待构建完成

### 2. 手动部署

如果自动部署失败，可以使用 Wrangler CLI：

```bash
# 安装 Wrangler
npm install -g wrangler

# 登录 Cloudflare
wrangler login

# 构建项目
npm run build

# 部署到 Pages
wrangler pages deploy dist --project-name=apikey-platform
```

## 🔧 构建问题解决

### 常见错误及解决方案

1. **terser not found 错误**：
   ```bash
   npm install terser --save-dev
   ```

2. **构建内存不足**：
   在 `package.json` 中增加内存限制：
   ```json
   {
     "scripts": {
       "build": "NODE_OPTIONS='--max-old-space-size=4096' vite build"
     }
   }
   ```

3. **依赖版本冲突**：
   ```bash
   npm audit fix
   # 或强制修复
   npm audit fix --force
   ```

## 🌐 其他部署平台

### Vercel 部署

1. 安装 Vercel CLI：
   ```bash
   npm install -g vercel
   ```

2. 部署：
   ```bash
   vercel --prod
   ```

3. 配置文件 `vercel.json`：
   ```json
   {
     "buildCommand": "npm run build",
     "outputDirectory": "dist",
     "framework": "vite",
     "rewrites": [
       { "source": "/(.*)", "destination": "/index.html" }
     ]
   }
   ```

### Netlify 部署

1. 在项目根目录创建 `netlify.toml`：
   ```toml
   [build]
     command = "npm run build"
     publish = "dist"

   [[redirects]]
     from = "/*"
     to = "/index.html"
     status = 200
   ```

2. 连接 GitHub 仓库并部署

### GitHub Pages 部署

1. 安装 gh-pages：
   ```bash
   npm install --save-dev gh-pages
   ```

2. 在 `package.json` 中添加脚本：
   ```json
   {
     "scripts": {
       "deploy": "gh-pages -d dist"
     }
   }
   ```

3. 部署：
   ```bash
   npm run build
   npm run deploy
   ```

## 📋 部署前检查清单

- [ ] 所有依赖已安装
- [ ] 构建成功 (`npm run build`)
- [ ] SEO 配置已更新（域名等）
- [ ] 图片文件已添加到 public 目录
- [ ] 环境变量已配置
- [ ] 安全头部已设置

## 🔍 部署后验证

1. **功能测试**：
   - [ ] 页面正常加载
   - [ ] 路由切换正常
   - [ ] 响应式设计正常
   - [ ] 图片显示正常

2. **SEO 检查**：
   - [ ] 页面标题正确
   - [ ] Meta 描述存在
   - [ ] Open Graph 标签正确
   - [ ] Sitemap 可访问
   - [ ] Robots.txt 可访问

3. **性能检查**：
   - [ ] 页面加载速度 < 3秒
   - [ ] 静态资源缓存正常
   - [ ] 压缩正常

4. **安全检查**：
   - [ ] HTTPS 启用
   - [ ] 安全头部设置正确
   - [ ] CSP 策略正常

## 🚨 故障排除

### 构建失败

1. 检查 Node.js 版本（推荐 18+）
2. 清理缓存：`npm ci`
3. 检查依赖版本冲突
4. 查看构建日志

### 部署失败

1. 检查构建输出目录
2. 验证部署配置
3. 检查环境变量
4. 查看部署日志

### 运行时错误

1. 检查浏览器控制台
2. 验证路由配置
3. 检查静态资源路径
4. 验证 API 端点

## 📞 支持

如果遇到部署问题，请：

1. 检查本文档的故障排除部分
2. 查看项目的 Issues 页面
3. 创建新的 Issue 并提供详细信息

## 🔄 持续部署

项目已配置自动部署：
- 推送到 `main` 分支会触发自动部署
- 构建状态会在 GitHub 仓库中显示
- 部署完成后会自动更新线上版本
