import React from 'react';
import { useApp } from '../../contexts/AppContext';
import TopNavigation from './TopNavigation';
import PackageIntroduction from '../Packages/PackageIntroduction';
import Tutorial from '../Tutorial/Tutorial';
import ModelManagement from '../Models/ModelManagement';
import ApiKeyManagement from '../ApiKeys/ApiKeyManagement';
import SEOChecker from '../SEO/SEOChecker';

const Layout: React.FC = () => {
  const { state } = useApp();

  const renderCurrentView = () => {
    switch (state.currentView) {
      case 'packages':
        return <PackageIntroduction />;
      case 'tutorial':
        return <Tutorial />;
      case 'models':
        return <ModelManagement />;
      case 'api-keys':
        return <ApiKeyManagement />;
      default:
        return <PackageIntroduction />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {renderCurrentView()}
      </main>
      <SEOChecker />
    </div>
  );
};

export default Layout;