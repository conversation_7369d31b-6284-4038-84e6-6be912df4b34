// SEO配置文件
export interface SEOConfig {
  title: string;
  description: string;
  keywords: string[];
  ogImage?: string;
  canonical?: string;
}

// 默认SEO配置
export const defaultSEO: SEOConfig = {
  title: 'AI模型套餐平台 - OpenAI、Claude、DeepSeek等AI模型API服务',
  description: '专业的AI模型API服务平台，提供OpenAI GPT-4、Claude、DeepSeek、Gemini等主流AI模型套餐。支持沉浸式翻译、Cherry Studio等多种客户端，价格实惠，最低0.66折优惠。',
  keywords: [
    'AI模型', 'OpenAI', 'GPT-4', 'Claude', 'DeepSeek', 'API服务',
    'AI套餐', '沉浸式翻译', 'Cherry Studio', 'ChatBox',
    '人工智能', '机器学习', 'API密钥', 'AI工具', 'Gemini',
    'xAI', 'SillyTavern', '模型API', 'AI代理', 'ChatGPT'
  ],
  ogImage: '/og-image.png',
  canonical: 'https://your-domain.com'
};

// 页面特定的SEO配置
export const pageSEO: Record<string, SEOConfig> = {
  packages: {
    title: 'AI模型套餐价格 - 全模型支持，最低0.66折优惠',
    description: '查看AI模型套餐价格表，包含OpenAI GPT-4、Claude、DeepSeek专属套餐。全模型支持，价格实惠，1元起体验，最高享受0.66折优惠。',
    keywords: [
      'AI模型价格', '套餐价格', 'OpenAI价格', 'Claude价格', 'DeepSeek价格',
      'AI套餐', 'GPT-4价格', '模型费用', 'API价格', '优惠套餐'
    ],
    canonical: 'https://your-domain.com/packages'
  },
  tutorial: {
    title: 'AI模型使用教程 - 沉浸式翻译、Cherry Studio配置指南',
    description: '详细的AI模型使用教程，包括沉浸式翻译插件配置、Cherry Studio设置、ChatBox使用方法等。支持多种客户端，轻松接入AI模型API。',
    keywords: [
      'AI使用教程', '沉浸式翻译配置', 'Cherry Studio教程', 'ChatBox设置',
      'API配置', 'AI客户端', '模型配置', 'SillyTavern', '使用指南'
    ],
    canonical: 'https://your-domain.com/tutorial'
  },
  models: {
    title: 'AI模型列表 - OpenAI、Claude、DeepSeek等模型ID大全',
    description: '完整的AI模型ID列表，包含OpenAI GPT系列、Claude系列、DeepSeek、Gemini等主流AI模型的详细信息和费率说明。',
    keywords: [
      '模型ID', 'AI模型列表', 'OpenAI模型', 'Claude模型', 'DeepSeek模型',
      'Gemini模型', '模型费率', 'API模型', '模型参数', '模型对比'
    ],
    canonical: 'https://your-domain.com/models'
  },
  'api-keys': {
    title: 'API密钥管理 - 安全管理您的AI模型API密钥',
    description: 'API密钥管理工具，帮助您安全地管理和使用AI模型API密钥。支持密钥验证、使用量查询、安全存储等功能。',
    keywords: [
      'API密钥', '密钥管理', 'API安全', '密钥验证', '使用量查询',
      'API管理', '密钥存储', 'API工具', '安全管理'
    ],
    canonical: 'https://your-domain.com/api-keys'
  }
};

// 获取页面SEO配置
export const getPageSEO = (page: string): SEOConfig => {
  return pageSEO[page] || defaultSEO;
};

// 生成页面标题
export const generateTitle = (pageTitle?: string): string => {
  if (!pageTitle) return defaultSEO.title;
  return `${pageTitle} - AI模型套餐平台`;
};

// 生成关键词字符串
export const generateKeywords = (keywords: string[]): string => {
  return keywords.join(', ');
};

// 网站基础信息
export const siteInfo = {
  name: 'AI模型套餐平台',
  domain: 'your-domain.com', // 🚨 请替换为实际域名
  logo: '/logo.png', // 🚨 请确保文件存在
  ogImage: '/og-image.png', // 🚨 请创建1200x630px的OG图片
  themeColor: '#667eea',
  language: 'zh-CN',
  locale: 'zh_CN',
  // SEO相关配置
  author: 'AI模型套餐平台',
  copyright: `© ${new Date().getFullYear()} AI模型套餐平台. All rights reserved.`,
  contact: {
    email: '<EMAIL>', // 🚨 请替换为实际邮箱
    phone: '+86-xxx-xxxx-xxxx' // 🚨 请替换为实际电话
  }
};
