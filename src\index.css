@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式优化 */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-feature-settings: 'rlig' 1, 'calt' 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* 移动端优化 */
  @media (max-width: 768px) {
    body {
      font-size: 16px; /* 防止 iOS Safari 缩放 */
    }
  }
}

/* 自定义滚动条 */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
}

/* 响应式表格 */
@layer components {
  .responsive-table {
    @apply min-w-full overflow-x-auto;
  }
  
  .responsive-table table {
    @apply w-full;
  }
  
  @media (max-width: 640px) {
    .responsive-table {
      @apply text-sm;
    }
    
    .responsive-table th,
    .responsive-table td {
      @apply px-2 py-2;
    }
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  /* 确保移动端布局不会溢出 */
  .mobile-container {
    @apply px-4 py-4;
  }
  
  /* 移动端卡片间距调整 */
  .mobile-card {
    @apply mx-2 mb-4;
  }
  
  /* 移动端按钮优化 */
  .mobile-button {
    @apply min-h-[44px] px-4 py-2;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .bg-gray-50 {
    @apply bg-white;
  }
  
  .text-gray-600 {
    @apply text-gray-900;
  }
  
  .border-gray-200 {
    @apply border-gray-400;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}