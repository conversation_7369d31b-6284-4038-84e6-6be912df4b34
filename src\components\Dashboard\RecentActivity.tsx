import React from 'react';
import { Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

const activities = [
  {
    id: 1,
    type: 'success',
    message: 'GPT-4 Turbo 请求完成',
    timestamp: '2分钟前',
    icon: CheckCircle,
    color: 'text-green-500'
  },
  {
    id: 2,
    type: 'success',
    message: '新建API密钥: Dev-Key-2024',
    timestamp: '5分钟前',
    icon: CheckCircle,
    color: 'text-green-500'
  },
  {
    id: 3,
    type: 'warning',
    message: 'Claude-3 达到速率限制',
    timestamp: '10分钟前',
    icon: AlertCircle,
    color: 'text-yellow-500'
  },
  {
    id: 4,
    type: 'error',
    message: 'DALL-E 3 请求失败',
    timestamp: '15分钟前',
    icon: XCircle,
    color: 'text-red-500'
  },
  {
    id: 5,
    type: 'success',
    message: '模型端点已更新',
    timestamp: '20分钟前',
    icon: CheckCircle,
    color: 'text-green-500'
  }
];

const RecentActivity: React.FC = () => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">最近活动</h3>
          <Clock className="w-5 h-5 text-gray-400" />
        </div>
      </div>
      <div className="p-6">
        <div className="space-y-4">
          {activities.map((activity) => {
            const Icon = activity.icon;
            return (
              <div key={activity.id} className="flex items-start space-x-3">
                <Icon className={`w-5 h-5 mt-0.5 ${activity.color}`} />
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500 mt-1">{activity.timestamp}</p>
                </div>
              </div>
            );
          })}
        </div>
        <button className="w-full mt-4 text-sm text-blue-600 hover:text-blue-700 font-medium">
          查看所有活动
        </button>
      </div>
    </div>
  );
};

export default RecentActivity;