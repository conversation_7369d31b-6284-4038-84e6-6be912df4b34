import React, { useState } from 'react';
import { X } from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { Model } from '../../types';

interface ModelFormProps {
  model?: Model | null;
  onClose: () => void;
}

const ModelForm: React.FC<ModelFormProps> = ({ model, onClose }) => {
  const { dispatch } = useApp();
  
  const [formData, setFormData] = useState({
    name: model?.name || '',
    provider: model?.provider || '',
    type: model?.type || 'text',
    status: model?.status || 'active',
    apiEndpoint: model?.apiEndpoint || '',
    maxTokens: model?.maxTokens?.toString() || '',
    costPerToken: model?.costPerToken?.toString() || '',
    description: model?.description || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const newModel: Model = {
      id: model?.id || Date.now().toString(),
      name: formData.name,
      provider: formData.provider,
      type: formData.type as Model['type'],
      status: formData.status as Model['status'],
      apiEndpoint: formData.apiEndpoint,
      maxTokens: parseInt(formData.maxTokens),
      costPerToken: parseFloat(formData.costPerToken),
      description: formData.description,
      createdAt: model?.createdAt || new Date().toISOString(),
      lastUsed: model?.lastUsed || new Date().toISOString(),
      totalRequests: model?.totalRequests || 0,
      successRate: model?.successRate || 100
    };

    if (model) {
      dispatch({ type: 'UPDATE_MODEL', payload: newModel });
    } else {
      dispatch({ type: 'ADD_MODEL', payload: newModel });
    }

    onClose();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              {model ? '编辑模型' : '添加新模型'}
            </h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                模型名称
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="例如：GPT-4 Turbo"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                提供商
              </label>
              <input
                type="text"
                name="provider"
                value={formData.provider}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="例如：OpenAI"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                类型
              </label>
              <select
                name="type"
                value={formData.type}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="text">文本</option>
                <option value="image">图像</option>
                <option value="audio">音频</option>
                <option value="multimodal">多模态</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                状态
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="active">活跃</option>
                <option value="inactive">停用</option>
                <option value="maintenance">维护中</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                最大令牌数
              </label>
              <input
                type="number"
                name="maxTokens"
                value={formData.maxTokens}
                onChange={handleChange}
                required
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="4096"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                每令牌成本 ($)
              </label>
              <input
                type="number"
                name="costPerToken"
                value={formData.costPerToken}
                onChange={handleChange}
                required
                step="0.000001"
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="0.00003"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              API端点
            </label>
            <input
              type="url"
              name="apiEndpoint"
              value={formData.apiEndpoint}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="https://api.example.com/v1/chat/completions"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              描述
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="描述模型的功能和使用场景..."
            />
          </div>

          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {model ? '更新模型' : '添加模型'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ModelForm;