import { useEffect } from 'react';
import { SEOConfig, getPageSEO, generateKeywords, siteInfo } from '../config/seo';

// 自定义Hook用于管理页面SEO
export const useSEO = (page?: string, customConfig?: Partial<SEOConfig>) => {
  useEffect(() => {
    // 获取页面SEO配置
    const baseConfig = page ? getPageSEO(page) : getPageSEO('default');
    
    // 合并自定义配置
    const config: SEOConfig = {
      ...baseConfig,
      ...customConfig
    };

    // 更新页面标题
    document.title = config.title;

    // 更新meta标签的通用函数
    const updateMetaTag = (name: string, content: string, property?: boolean) => {
      const attribute = property ? 'property' : 'name';
      let meta = document.querySelector(`meta[${attribute}="${name}"]`) as HTMLMetaElement;
      
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute(attribute, name);
        document.head.appendChild(meta);
      }
      
      meta.setAttribute('content', content);
    };

    // 更新基础SEO标签
    updateMetaTag('description', config.description);
    updateMetaTag('keywords', generateKeywords(config.keywords));

    // 更新Open Graph标签
    updateMetaTag('og:title', config.title, true);
    updateMetaTag('og:description', config.description, true);
    updateMetaTag('og:type', 'website', true);
    updateMetaTag('og:site_name', siteInfo.name, true);

    if (config.canonical) {
      updateMetaTag('og:url', config.canonical, true);
    }

    if (config.ogImage) {
      updateMetaTag('og:image', config.ogImage, true);
    }

    // 更新Twitter标签
    updateMetaTag('twitter:title', config.title, true);
    updateMetaTag('twitter:description', config.description, true);
    
    if (config.canonical) {
      updateMetaTag('twitter:url', config.canonical, true);
    }

    if (config.ogImage) {
      updateMetaTag('twitter:image', config.ogImage, true);
    }

    // 更新canonical链接
    if (config.canonical) {
      let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
      
      if (!canonical) {
        canonical = document.createElement('link');
        canonical.setAttribute('rel', 'canonical');
        document.head.appendChild(canonical);
      }
      
      canonical.setAttribute('href', config.canonical);
    }

  }, [page, customConfig]);
};

// 设置页面标题的简化Hook
export const usePageTitle = (title: string) => {
  useEffect(() => {
    const fullTitle = `${title} - ${siteInfo.name}`;
    document.title = fullTitle;
    
    // 同时更新og:title
    const ogTitle = document.querySelector('meta[property="og:title"]') as HTMLMetaElement;
    if (ogTitle) {
      ogTitle.setAttribute('content', fullTitle);
    }
  }, [title]);
};

// 设置页面描述的Hook
export const usePageDescription = (description: string) => {
  useEffect(() => {
    const updateMetaTag = (name: string, content: string, property?: boolean) => {
      const attribute = property ? 'property' : 'name';
      let meta = document.querySelector(`meta[${attribute}="${name}"]`) as HTMLMetaElement;
      
      if (meta) {
        meta.setAttribute('content', content);
      }
    };

    updateMetaTag('description', description);
    updateMetaTag('og:description', description, true);
    updateMetaTag('twitter:description', description, true);
  }, [description]);
};
