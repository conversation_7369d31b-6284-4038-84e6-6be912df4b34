import React, { useEffect } from 'react';
import { SEOConfig, siteInfo, generateKeywords } from '../../config/seo';

interface SEOHeadProps {
  config: SEOConfig;
  page?: string;
}

const SEOHead: React.FC<SEOHeadProps> = ({ config, page }) => {
  useEffect(() => {
    // 更新页面标题
    document.title = config.title;

    // 更新或创建meta标签的通用函数
    const updateMetaTag = (name: string, content: string, property?: boolean) => {
      const attribute = property ? 'property' : 'name';
      let meta = document.querySelector(`meta[${attribute}="${name}"]`) as HTMLMetaElement;
      
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute(attribute, name);
        document.head.appendChild(meta);
      }
      
      meta.setAttribute('content', content);
    };

    // 更新基础SEO标签
    updateMetaTag('description', config.description);
    updateMetaTag('keywords', generateKeywords(config.keywords));

    // 更新Open Graph标签
    updateMetaTag('og:title', config.title, true);
    updateMetaTag('og:description', config.description, true);
    updateMetaTag('og:type', 'website', true);
    updateMetaTag('og:site_name', siteInfo.name, true);
    updateMetaTag('og:locale', siteInfo.locale, true);

    if (config.canonical) {
      updateMetaTag('og:url', config.canonical, true);
    }

    if (config.ogImage) {
      updateMetaTag('og:image', config.ogImage, true);
      updateMetaTag('og:image:width', '1200', true);
      updateMetaTag('og:image:height', '630', true);
    }

    // 更新Twitter标签
    updateMetaTag('twitter:card', 'summary_large_image', true);
    updateMetaTag('twitter:title', config.title, true);
    updateMetaTag('twitter:description', config.description, true);
    
    if (config.canonical) {
      updateMetaTag('twitter:url', config.canonical, true);
    }

    if (config.ogImage) {
      updateMetaTag('twitter:image', config.ogImage, true);
    }

    // 更新canonical链接
    if (config.canonical) {
      let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
      
      if (!canonical) {
        canonical = document.createElement('link');
        canonical.setAttribute('rel', 'canonical');
        document.head.appendChild(canonical);
      }
      
      canonical.setAttribute('href', config.canonical);
    }

    // 添加结构化数据（如果是特定页面）
    if (page) {
      addStructuredData(page, config);
    }

  }, [config, page]);

  // 添加页面特定的结构化数据
  const addStructuredData = (page: string, config: SEOConfig) => {
    // 移除现有的结构化数据
    const existingScript = document.querySelector('script[data-page-structured-data]');
    if (existingScript) {
      existingScript.remove();
    }

    let structuredData: any = null;

    switch (page) {
      case 'packages':
        structuredData = {
          "@context": "https://schema.org",
          "@type": "Service",
          "name": "AI模型API套餐服务",
          "description": config.description,
          "provider": {
            "@type": "Organization",
            "name": siteInfo.name
          },
          "offers": [
            {
              "@type": "Offer",
              "name": "1元体验套餐",
              "price": "0.8",
              "priceCurrency": "CNY"
            },
            {
              "@type": "Offer", 
              "name": "10元限时套餐",
              "price": "6.66",
              "priceCurrency": "CNY"
            },
            {
              "@type": "Offer",
              "name": "20刀套餐",
              "price": "17",
              "priceCurrency": "CNY"
            }
          ]
        };
        break;

      case 'tutorial':
        structuredData = {
          "@context": "https://schema.org",
          "@type": "HowTo",
          "name": "AI模型使用教程",
          "description": config.description,
          "step": [
            {
              "@type": "HowToStep",
              "name": "配置沉浸式翻译插件",
              "text": "学习如何配置沉浸式翻译插件使用AI模型"
            },
            {
              "@type": "HowToStep", 
              "name": "设置Cherry Studio",
              "text": "配置Cherry Studio客户端连接AI模型API"
            },
            {
              "@type": "HowToStep",
              "name": "使用ChatBox",
              "text": "在ChatBox中配置和使用AI模型"
            }
          ]
        };
        break;

      case 'models':
        structuredData = {
          "@context": "https://schema.org",
          "@type": "ItemList",
          "name": "AI模型列表",
          "description": config.description,
          "itemListElement": [
            {
              "@type": "SoftwareApplication",
              "name": "OpenAI GPT-4",
              "applicationCategory": "AI Model"
            },
            {
              "@type": "SoftwareApplication",
              "name": "Claude",
              "applicationCategory": "AI Model"
            },
            {
              "@type": "SoftwareApplication",
              "name": "DeepSeek",
              "applicationCategory": "AI Model"
            }
          ]
        };
        break;
    }

    if (structuredData) {
      const script = document.createElement('script');
      script.type = 'application/ld+json';
      script.setAttribute('data-page-structured-data', 'true');
      script.textContent = JSON.stringify(structuredData);
      document.head.appendChild(script);
    }
  };

  // 这个组件不渲染任何内容，只是管理head标签
  return null;
};

export default SEOHead;
