<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片加载测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-item { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .test-item h3 { margin: 0 0 10px 0; }
        .test-item img { max-width: 300px; border: 1px solid #ccc; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>图片加载测试页面</h1>
    <p>这个页面用于测试图片是否能正常加载</p>

    <div class="test-item">
        <h3>测试1: xianyu-shop.png</h3>
        <img src="/xianyu-shop.png" alt="闲鱼店铺" onload="showSuccess(this)" onerror="showError(this)">
        <div class="status" id="status1"></div>
    </div>

    <div class="test-item">
        <h3>测试2: immersive-translate-step1.png</h3>
        <img src="/immersive-translate-step1.png" alt="沉浸式翻译步骤1" onload="showSuccess(this)" onerror="showError(this)">
        <div class="status" id="status2"></div>
    </div>

    <div class="test-item">
        <h3>测试3: chatbox-step1.png</h3>
        <img src="/chatbox-step1.png" alt="ChatBox步骤1" onload="showSuccess(this)" onerror="showError(this)">
        <div class="status" id="status3"></div>
    </div>

    <div class="test-item">
        <h3>测试4: 绝对路径测试</h3>
        <img src="https://your-domain.pages.dev/xianyu-shop.png" alt="绝对路径测试" onload="showSuccess(this)" onerror="showError(this)">
        <div class="status" id="status4"></div>
        <p><small>请将 your-domain 替换为你的实际域名</small></p>
    </div>

    <script>
        function showSuccess(img) {
            const status = img.nextElementSibling;
            status.innerHTML = '<span class="success">✅ 图片加载成功</span>';
            status.innerHTML += `<br><small>尺寸: ${img.naturalWidth}x${img.naturalHeight}</small>`;
        }

        function showError(img) {
            const status = img.nextElementSibling;
            status.innerHTML = '<span class="error">❌ 图片加载失败</span>';
            status.innerHTML += `<br><small>路径: ${img.src}</small>`;
        }

        // 页面加载完成后的检查
        window.onload = function() {
            console.log('页面加载完成，开始检查图片...');
            
            // 检查所有图片的加载状态
            const images = document.querySelectorAll('img');
            images.forEach((img, index) => {
                console.log(`图片 ${index + 1}: ${img.src}`);
                console.log(`- 完整路径: ${img.complete}`);
                console.log(`- 自然宽度: ${img.naturalWidth}`);
                console.log(`- 自然高度: ${img.naturalHeight}`);
            });

            // 检查当前页面的协议和域名
            console.log('当前页面信息:');
            console.log(`- 协议: ${window.location.protocol}`);
            console.log(`- 域名: ${window.location.hostname}`);
            console.log(`- 完整URL: ${window.location.href}`);
        };
    </script>
</body>
</html>
