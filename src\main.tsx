import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';

// 确保 DOM 已加载
const rootElement = document.getElementById('root');

if (!rootElement) {
  throw new Error('Root element not found');
}

// 创建 React 应用
const root = createRoot(rootElement);

// 渲染应用
root.render(
  <StrictMode>
    <App />
  </StrictMode>
);

// 隐藏加载指示器（备用方案）
const hideLoadingIndicator = () => {
  const loadingIndicator = document.getElementById('loading-indicator');
  if (loadingIndicator) {
    loadingIndicator.classList.add('loading-fade-out');
    setTimeout(() => {
      if (loadingIndicator.parentNode) {
        loadingIndicator.parentNode.removeChild(loadingIndicator);
      }
    }, 500);
  }
};

// React 应用渲染完成后隐藏加载指示器
setTimeout(hideLoadingIndicator, 100);