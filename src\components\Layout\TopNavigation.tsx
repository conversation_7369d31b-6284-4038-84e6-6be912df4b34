import React, { useState, useRef, useEffect } from 'react';
import {
  ChevronDown,
  Home,
  ShoppingCart,
  Bot,
  BookOpen,
  BarChart3,
  Settings,
  Zap,
  MessageSquare,
  Key,
  TrendingUp,
  Globe,
  Monitor,
  Star,
  Phone,
  Link
} from 'lucide-react';
import { useApp } from '../../contexts/AppContext';

interface MenuItem {
  id: string;
  label: string;
  icon?: React.ComponentType<any>;
  children?: MenuItem[];
  action?: () => void;
}

const menuItems: MenuItem[] = [
  {
    id: 'home',
    label: '首页',
    icon: Home
  },
  {
    id: 'help',
    label: '使用帮助',
    icon: BookOpen,
    children: [
      {
        id: 'api-address',
        label: 'API地址',
        icon: Link
      },
      {
        id: 'client-config',
        label: '客户端配置',
        icon: Monitor,
        children: [
          { id: 'immersive-translate', label: '沉浸式翻译插件' },
          { id: 'cherry-studio', label: 'Cherry Studio（推荐）' },
          { id: 'chatbox', label: 'ChatBoX' },
          { id: 'sillytavern', label: 'SillyTavern(酒馆)' },
          { id: 'more-clients', label: '更多客户端' }
        ]
      },
      {
        id: 'recommended-models',
        label: '推荐模型',
        icon: Star
      },
      {
        id: 'contact-purchase',
        label: '联系购买',
        icon: Phone
      }
    ]
  },
  {
    id: 'models',
    label: '模型列表',
    icon: Bot,
    children: [
      { id: 'openai-models', label: 'OpenAI' },
      { id: 'anthropic-models', label: 'Anthropic' },
      { id: 'google-models', label: 'Google' },
      { id: 'deepseek-models', label: 'DeepSeek' },
      { id: 'xai-models', label: 'XAI' },
      { id: 'more-models', label: '更多模型' }
    ]
  },
  {
    id: 'api-keys',
    label: 'API使用查询',
    icon: BarChart3
  }
];

const TopNavigation: React.FC = () => {
  const { state, dispatch } = useApp();
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [mobileExpandedItems, setMobileExpandedItems] = useState<Set<string>>(new Set());
  const timeoutRef = useRef<number | null>(null);

  const handleMouseEnter = (menuId: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setActiveDropdown(menuId);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setActiveDropdown(null);
      setActiveSubmenu(null);
    }, 300);
  };

  const handleSubmenuEnter = (submenuId: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setActiveSubmenu(submenuId);
  };



  const toggleMobileItem = (itemId: string) => {
    const newExpanded = new Set(mobileExpandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setMobileExpandedItems(newExpanded);
  };

  const handleMenuClick = (menuItem: MenuItem) => {
    // 处理菜单点击逻辑
    if (menuItem.action) {
      menuItem.action();
    } else {
      // 根据菜单ID设置当前视图
      const viewMapping: Record<string, string> = {
        'home': 'packages',
        'api-address': 'tutorial',
        'client-config': 'tutorial',
        'immersive-translate': 'tutorial',
        'cherry-studio': 'tutorial',
        'chatbox': 'tutorial',
        'sillytavern': 'tutorial',
        'more-clients': 'tutorial',
        'recommended-models': 'tutorial',
        'contact-purchase': 'tutorial',
        'models': 'models',
        'openai-models': 'models',
        'anthropic-models': 'models',
        'google-models': 'models',
        'deepseek-models': 'models',
        'xai-models': 'models',
        'more-models': 'more-models',
        'api-keys': 'api-keys'
      };

      // Tutorial页面的子页面映射
      const tutorialSectionMapping: Record<string, string> = {
        'api-address': 'api',
        'client-config': 'help',
        'immersive-translate': 'help',
        'cherry-studio': 'help',
        'chatbox': 'help',
        'sillytavern': 'help',
        'more-clients': 'help',
        'recommended-models': 'models',
        'contact-purchase': 'updates'
      };

      // 客户端配置映射
      const clientMapping: Record<string, string> = {
        'immersive-translate': 'immersive-translate',
        'cherry-studio': 'cherry-studio',
        'chatbox': 'chatbox',
        'sillytavern': 'sillytavern',
        'more-clients': 'more-clients'
      };

      // 供应商映射
      const providerMapping: Record<string, string> = {
        'openai-models': 'OpenAI',
        'anthropic-models': 'Anthropic',
        'google-models': 'Google',
        'deepseek-models': 'DeepSeek',
        'xai-models': 'xAI'
      };

      const view = viewMapping[menuItem.id] || 'packages';
      dispatch({ type: 'SET_CURRENT_VIEW', payload: view });

      // 如果是tutorial页面，设置子页面
      if (view === 'tutorial') {
        const section = tutorialSectionMapping[menuItem.id] || 'api';
        dispatch({ type: 'SET_TUTORIAL_SECTION', payload: section });

        // 如果是客户端配置，设置具体的客户端
        if (clientMapping[menuItem.id]) {
          dispatch({ type: 'SET_TUTORIAL_CLIENT', payload: clientMapping[menuItem.id] });
        }
      }

      // 如果是models页面，设置选中的供应商
      if (view === 'models' && providerMapping[menuItem.id]) {
        dispatch({ type: 'SET_SELECTED_PROVIDER', payload: providerMapping[menuItem.id] });
      }
    }

    setActiveDropdown(null);
    setActiveSubmenu(null);
    setMobileMenuOpen(false);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const renderMenuItem = (item: MenuItem, level: number = 1) => {
    const hasChildren = item.children && item.children.length > 0;
    const isActive = activeDropdown === item.id || activeSubmenu === item.id;
    const Icon = item.icon;

    // 检查当前菜单项是否对应当前视图
    const isCurrentView = () => {
      const viewMapping: Record<string, string> = {
        'home': 'packages',
        'api-address': 'tutorial',
        'client-config': 'tutorial',
        'immersive-translate': 'tutorial',
        'cherry-studio': 'tutorial',
        'chatbox': 'tutorial',
        'sillytavern': 'tutorial',
        'more-clients': 'tutorial',
        'recommended-models': 'tutorial',
        'contact-purchase': 'tutorial',
        'models': 'models',
        'openai-models': 'models',
        'anthropic-models': 'models',
        'google-models': 'models',
        'deepseek-models': 'models',
        'xai-models': 'models',
        'more-models': 'more-models',
        'api-keys': 'api-keys'
      };

      const mappedView = viewMapping[item.id];
      if (mappedView !== state.currentView) {
        return false;
      }

      // 如果是tutorial页面，需要进一步检查子页面和客户端
      if (mappedView === 'tutorial') {
        const tutorialSectionMapping: Record<string, string> = {
          'api-address': 'api',
          'client-config': 'help',
          'immersive-translate': 'help',
          'cherry-studio': 'help',
          'chatbox': 'help',
          'sillytavern': 'help',
          'more-clients': 'help',
          'recommended-models': 'models',
          'contact-purchase': 'updates'
        };

        const clientMapping: Record<string, string> = {
          'immersive-translate': 'immersive-translate',
          'cherry-studio': 'cherry-studio',
          'chatbox': 'chatbox',
          'sillytavern': 'sillytavern',
          'more-clients': 'more-clients'
        };

        const expectedSection = tutorialSectionMapping[item.id];
        if (expectedSection && expectedSection !== state.tutorialSection) {
          return false;
        }

        const expectedClient = clientMapping[item.id];
        if (expectedClient && expectedClient !== state.tutorialClient) {
          return false;
        }
      }

      // 如果是models页面，需要进一步检查选中的供应商
      if (mappedView === 'models') {
        const providerMapping: Record<string, string> = {
          'openai-models': 'OpenAI',
          'anthropic-models': 'Anthropic',
          'google-models': 'Google',
          'deepseek-models': 'DeepSeek',
          'xai-models': 'xAI'
        };

        const expectedProvider = providerMapping[item.id];
        if (expectedProvider && expectedProvider !== state.selectedProvider) {
          return false;
        }
      }

      return true;
    };

    return (
      <div
        key={item.id}
        className="relative"
        onMouseEnter={() => level === 1 ? handleMouseEnter(item.id) : handleSubmenuEnter(item.id)}
        onMouseLeave={level === 1 ? handleMouseLeave : undefined}
      >
        <button
          onClick={() => !hasChildren && handleMenuClick(item)}
          className={`flex items-center space-x-2 px-4 py-2 text-sm font-medium transition-colors duration-200 ${
            level === 1
              ? isCurrentView()
                ? 'text-blue-600 bg-blue-50 rounded-md'
                : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md'
              : level === 2
              ? isCurrentView()
                ? 'text-blue-600 bg-blue-50 w-full text-left'
                : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50 w-full text-left'
              : isCurrentView()
              ? 'text-blue-600 bg-blue-50 w-full text-left pl-6'
              : 'text-gray-500 hover:text-blue-600 hover:bg-gray-50 w-full text-left pl-6'
          }`}
        >
          {Icon && level === 1 && <Icon className="w-4 h-4" />}
          <span>{item.label}</span>
          {hasChildren && <ChevronDown className="w-4 h-4 ml-1" />}
        </button>

        {/* 下拉菜单 */}
        {hasChildren && isActive && (
          <div
            className={`absolute ${
              level === 1 ? 'z-50' : 'z-[60]'
            } ${
              level === 1
                ? 'top-full left-0 mt-1'
                : 'top-0 left-full ml-0'
            } bg-white border border-gray-200 rounded-lg shadow-xl min-w-48 max-w-64`}
            onMouseEnter={() => {
              if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
              }
              if (level === 1) {
                handleMouseEnter(item.id);
              } else {
                handleSubmenuEnter(item.id);
              }
            }}
            onMouseLeave={() => {
              if (level === 1) {
                handleMouseLeave();
              } else {
                timeoutRef.current = setTimeout(() => {
                  setActiveSubmenu(null);
                }, 200);
              }
            }}
          >
            <div className="py-2">
              {item.children?.map((child) => (
                <div key={child.id} className="relative">
                  {child.children ? (
                    <div
                      className="relative"
                      onMouseEnter={() => {
                        if (timeoutRef.current) {
                          clearTimeout(timeoutRef.current);
                        }
                        handleSubmenuEnter(child.id);
                      }}
                      onMouseLeave={() => {
                        timeoutRef.current = setTimeout(() => {
                          setActiveSubmenu(null);
                        }, 200);
                      }}
                    >
                      <button
                        className={`flex items-center justify-between w-full text-left px-4 py-3 text-sm text-gray-600 hover:text-blue-600 hover:bg-gray-50 transition-colors duration-150 ${
                          activeSubmenu === child.id ? 'bg-gray-50 text-blue-600' : ''
                        }`}
                        onMouseEnter={() => {
                          if (timeoutRef.current) {
                            clearTimeout(timeoutRef.current);
                          }
                          handleSubmenuEnter(child.id);
                        }}
                      >
                        <div className="flex items-center">
                          {child.icon && <child.icon className="w-4 h-4 mr-2" />}
                          <span>{child.label}</span>
                        </div>
                        <ChevronDown className="w-4 h-4 transform rotate-[-90deg]" />
                      </button>

                      {/* 三级菜单 */}
                      {activeSubmenu === child.id && child.children && (
                        <div
                          className="absolute top-0 left-full z-[70] bg-white border border-gray-200 rounded-lg shadow-xl min-w-48 ml-0"
                          onMouseEnter={() => {
                            if (timeoutRef.current) {
                              clearTimeout(timeoutRef.current);
                            }
                            handleSubmenuEnter(child.id);
                          }}
                          onMouseLeave={() => {
                            timeoutRef.current = setTimeout(() => {
                              setActiveSubmenu(null);
                            }, 200);
                          }}
                        >
                          <div className="py-2">
                            {child.children.map((grandchild) => (
                              <button
                                key={grandchild.id}
                                onClick={() => handleMenuClick(grandchild)}
                                className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-gray-50 transition-colors duration-150"
                                onMouseEnter={() => {
                                  if (timeoutRef.current) {
                                    clearTimeout(timeoutRef.current);
                                  }
                                }}
                              >
                                {grandchild.label}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <button
                      onClick={() => handleMenuClick(child)}
                      className={`flex items-center w-full text-left px-4 py-3 text-sm text-gray-600 hover:text-blue-600 hover:bg-gray-50 transition-colors duration-150`}
                      onMouseEnter={() => {
                        if (timeoutRef.current) {
                          clearTimeout(timeoutRef.current);
                        }
                      }}
                    >
                      {child.icon && <child.icon className="w-4 h-4 mr-2" />}
                      {child.label}
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <nav className="bg-white border-b border-gray-200 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <button
              onClick={() => {
                dispatch({ type: 'SET_CURRENT_VIEW', payload: 'packages' });
                setActiveDropdown(null);
                setActiveSubmenu(null);
                setMobileMenuOpen(false);
              }}
              className="flex-shrink-0 flex items-center hover:opacity-80 transition-opacity cursor-pointer"
            >
              <Zap className="w-8 h-8 text-blue-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">AI模型平台</span>
            </button>
          </div>

          {/* 主导航菜单 */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              {menuItems.map((item) => renderMenuItem(item))}
            </div>
          </div>

          {/* 移动端菜单按钮 */}
          <div className="md:hidden">
            <button
              type="button"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              aria-controls="mobile-menu"
              aria-expanded={mobileMenuOpen}
            >
              <span className="sr-only">打开主菜单</span>
              {mobileMenuOpen ? (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 移动端菜单 */}
      {mobileMenuOpen && (
        <div className="md:hidden">
          <div className="px-4 pt-2 pb-3 space-y-2 bg-white border-t border-gray-200 max-h-[70vh] overflow-y-auto">
            {menuItems.map((item) => (
              <div key={item.id} className="space-y-1">
                <button
                  onClick={() => {
                    if (!item.children) {
                      handleMenuClick(item);
                    } else {
                      toggleMobileItem(item.id);
                    }
                  }}
                  className="flex items-center justify-between w-full px-4 py-3 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                >
                  <div className="flex items-center">
                    {item.icon && <item.icon className="w-5 h-5 mr-3" />}
                    {item.label}
                  </div>
                  {item.children && (
                    <ChevronDown
                      className={`w-4 h-4 transition-transform ${
                        mobileExpandedItems.has(item.id) ? 'rotate-180' : ''
                      }`}
                    />
                  )}
                </button>

                {/* 二级菜单 */}
                {item.children && mobileExpandedItems.has(item.id) && (
                  <div className="ml-4 space-y-1 border-l-2 border-gray-100 pl-4">
                    {item.children.map((child) => (
                      <div key={child.id} className="space-y-1">
                        <button
                          onClick={() => {
                            if (!child.children) {
                              handleMenuClick(child);
                            } else {
                              toggleMobileItem(child.id);
                            }
                          }}
                          className="flex items-center justify-between w-full text-left px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                        >
                          <div className="flex items-center">
                            {child.icon && <child.icon className="w-4 h-4 mr-2" />}
                            {child.label}
                          </div>
                          {child.children && (
                            <ChevronDown
                              className={`w-3 h-3 transition-transform ${
                                mobileExpandedItems.has(child.id) ? 'rotate-180' : ''
                              }`}
                            />
                          )}
                        </button>

                        {/* 三级菜单 */}
                        {child.children && mobileExpandedItems.has(child.id) && (
                          <div className="ml-4 space-y-1 border-l-2 border-gray-100 pl-3">
                            {child.children.map((grandchild) => (
                              <button
                                key={grandchild.id}
                                onClick={() => {
                                  handleMenuClick(grandchild);
                                }}
                                className="block w-full text-left px-3 py-2 text-sm text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                              >
                                {grandchild.label}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </nav>
  );
};

export default TopNavigation;
