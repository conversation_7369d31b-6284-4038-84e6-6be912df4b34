import React from 'react';
import { BarChart3 } from 'lucide-react';

const UsageChart: React.FC = () => {
  const data = [
    { day: '周一', requests: 120 },
    { day: '周二', requests: 150 },
    { day: '周三', requests: 180 },
    { day: '周四', requests: 200 },
    { day: '周五', requests: 170 },
    { day: '周六', requests: 90 },
    { day: '周日', requests: 110 }
  ];

  const maxRequests = Math.max(...data.map(d => d.requests));

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">本周API使用情况</h3>
          <BarChart3 className="w-5 h-5 text-gray-400" />
        </div>
      </div>
      <div className="p-6">
        <div className="flex items-end space-x-2 h-64">
          {data.map((item, index) => (
            <div key={index} className="flex-1 flex flex-col items-center">
              <div 
                className="w-full bg-blue-500 rounded-t-md transition-all duration-300 hover:bg-blue-600"
                style={{ 
                  height: `${(item.requests / maxRequests) * 200}px`,
                  minHeight: '4px'
                }}
                title={`${item.requests} 次请求`}
              ></div>
              <span className="text-xs text-gray-600 mt-2">{item.day}</span>
            </div>
          ))}
        </div>
        <div className="mt-4 flex justify-between text-sm text-gray-600">
          <span>总计: {data.reduce((sum, d) => sum + d.requests, 0)} 次请求</span>
          <span>平均: {Math.round(data.reduce((sum, d) => sum + d.requests, 0) / data.length)} 次/天</span>
        </div>
      </div>
    </div>
  );
};

export default UsageChart;