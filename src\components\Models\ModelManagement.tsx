import React, { useState } from 'react';
import { Search, Copy, ExternalLink, Star, Zap, DollarSign, TrendingUp, ChevronRight, Building2, Download } from 'lucide-react';
import { useSEO } from '../../hooks/useSEO';
import { useApp } from '../../contexts/AppContext';

interface ModelData {
  id: string;
  name: string;
  modelId: string;
  provider: string;
  category: string;
  inputPrice: string;
  outputPrice: string;
  contextLength?: number;
  description: string;
  features: string[];
  isRecommended?: boolean;
  isNew?: boolean;
  isPopular?: boolean;
}

const ModelManagement: React.FC = () => {
  // 设置页面SEO
  useSEO('models');

  const { state, dispatch } = useApp();
  const [searchTerm, setSearchTerm] = useState('');

  const selectedProvider = state.selectedProvider;
  const setSelectedProvider = (provider: string) => {
    dispatch({ type: 'SET_SELECTED_PROVIDER', payload: provider });
  };

  // 严格按照附件内容的模型数据
  const models: ModelData[] = [
    // OpenAI 模型
    { id: '1', name: 'o3-mini', modelId: 'o3-mini', provider: 'OpenAI', category: '文本生成', inputPrice: 'Input: $1.1 / M tokens', outputPrice: 'Output: $4.4 / M tokens', description: '最新的o3系列轻量版模型', features: ['推理能力', '经济实惠'], isNew: true },
    { id: '2', name: 'chatgpt-4o-latest', modelId: 'chatgpt-4o-latest', provider: 'OpenAI', category: '文本生成', inputPrice: 'Input: $5 / M tokens', outputPrice: 'Output: $15 / M tokens', description: 'ChatGPT-4o最新版本', features: ['最新版本', '高性能'], isRecommended: true },
    { id: '3', name: 'gpt-4o-2024-11-20', modelId: 'gpt-4o-2024-11-20', provider: 'OpenAI', category: '文本生成', inputPrice: 'Input: $2.5 / M tokens', outputPrice: 'Output: $10 / M tokens', description: 'GPT-4o 2024年11月版本', features: ['多模态', '高性能'] },
    { id: '4', name: 'gpt-4o-2024-08-06', modelId: 'gpt-4o-2024-08-06', provider: 'OpenAI', category: '文本生成', inputPrice: 'Input: $2.5 / M tokens', outputPrice: 'Output: $10 / M tokens', description: 'GPT-4o 2024年8月版本', features: ['多模态', '稳定版本'] },
    { id: '5', name: 'gpt-4o-2024-05-13', modelId: 'gpt-4o-2024-05-13', provider: 'OpenAI', category: '文本生成', inputPrice: 'Input: $2.5 / M tokens', outputPrice: 'Output: $7.5 / M tokens', description: 'GPT-4o 2024年5月版本', features: ['多模态', '经典版本'] },
    { id: '6', name: 'gpt-4o', modelId: 'gpt-4o', provider: 'OpenAI', category: '文本生成', inputPrice: 'Input: $2.5 / M tokens', outputPrice: 'Output: $7.5 / M tokens', description: 'GPT-4o标准版本', features: ['多模态', '高性能'], isRecommended: true, isPopular: true },
    { id: '7', name: 'gpt-4o-mini', modelId: 'gpt-4o-mini', provider: 'OpenAI', category: '文本生成', inputPrice: 'Input: $0.15 / M tokens', outputPrice: 'Output: $0.6 / M tokens', description: 'GPT-4o轻量版本', features: ['经济实惠', '快速响应'], isRecommended: true },
    { id: '8', name: 'gpt-4o-mini-2024-07-18', modelId: 'gpt-4o-mini-2024-07-18', provider: 'OpenAI', category: '文本生成', inputPrice: 'Input: $0.15 / M tokens', outputPrice: 'Output: $0.6 / M tokens', description: 'GPT-4o Mini 2024年7月版本', features: ['经济实惠', '特定版本'] },
    { id: '9', name: 'gpt-4-turbo', modelId: 'gpt-4-turbo', provider: 'OpenAI', category: '文本生成', inputPrice: 'Input: $10 / M tokens', outputPrice: 'Output: $30 / M tokens', description: 'GPT-4 Turbo版本', features: ['高性能', '大上下文'] },
    { id: '10', name: 'gpt-4-turbo-2024-04-09', modelId: 'gpt-4-turbo-2024-04-09', provider: 'OpenAI', category: '文本生成', inputPrice: 'Input: $10 / M tokens', outputPrice: 'Output: $30 / M tokens', description: 'GPT-4 Turbo 2024年4月版本', features: ['高性能', '特定版本'] },
    { id: '11', name: 'gpt-4', modelId: 'gpt-4', provider: 'OpenAI', category: '文本生成', inputPrice: 'Input: $30 / M tokens', outputPrice: 'Output: $60 / M tokens', description: 'GPT-4标准版本', features: ['经典模型', '高质量'] },
    { id: '12', name: 'gpt-3.5-turbo', modelId: 'gpt-3.5-turbo', provider: 'OpenAI', category: '文本生成', inputPrice: 'Input: $1.5 / M tokens', outputPrice: 'Output: $1.995 / M tokens', description: 'GPT-3.5 Turbo版本', features: ['经济实惠', '快速'] },
    { id: '13', name: 'o1-preview', modelId: 'o1-preview', provider: 'OpenAI', category: '推理模型', inputPrice: 'Price: $0.6 / 次', outputPrice: '', description: 'O1推理模型预览版', features: ['推理专家', '按次计费'] },
    { id: '14', name: 'o1-mini', modelId: 'o1-mini', provider: 'OpenAI', category: '推理模型', inputPrice: 'Price: $0.15 / 次', outputPrice: '', description: 'O1推理模型轻量版', features: ['推理能力', '经济实惠'] },
    { id: '15', name: 'o1', modelId: 'o1', provider: 'OpenAI', category: '推理模型', inputPrice: 'Price: $0.6 / 次', outputPrice: '', description: 'O1推理模型标准版', features: ['推理专家', '按次计费'] },
    { id: '16', name: 'o1-pro', modelId: 'o1-pro', provider: 'OpenAI', category: '推理模型', inputPrice: 'Price: $1.2 / 次', outputPrice: '', description: 'O1推理模型专业版', features: ['顶级推理', '专业级'] },
    { id: '17', name: 'gpt-4.1', modelId: 'gpt-4.1', provider: 'OpenAI', category: '文本生成', inputPrice: 'Input: $2 / M tokens', outputPrice: 'Output: $8 / M tokens', description: 'GPT-4.1版本', features: ['升级版本', '性能提升'], isNew: true },
    { id: '18', name: 'o3', modelId: 'o3', provider: 'OpenAI', category: '推理模型', inputPrice: 'Price: $0.6 / 次', outputPrice: '', description: 'O3推理模型', features: ['最新推理', '按次计费'], isNew: true },
    { id: '19', name: 'o3-pro', modelId: 'o3-pro', provider: 'OpenAI', category: '推理模型', inputPrice: 'Price: $1.2 / 次', outputPrice: '', description: 'O3推理模型专业版', features: ['顶级推理', '专业级'], isNew: true },

    // Claude 模型
    { id: '20', name: 'claude-3-5-sonnet-20240620', modelId: 'claude-3-5-sonnet-20240620', provider: 'Anthropic', category: '文本生成', inputPrice: 'Input: $6 / M tokens', outputPrice: 'Output: $30 / M tokens', description: 'Claude 3.5 Sonnet 6月版本', features: ['长上下文', '推理能力'], isRecommended: true },
    { id: '21', name: 'claude-3-5-sonnet-20241022', modelId: 'claude-3-5-sonnet-20241022', provider: 'Anthropic', category: '文本生成', inputPrice: 'Input: $6 / M tokens', outputPrice: 'Output: $30 / M tokens', description: 'Claude 3.5 Sonnet 10月版本', features: ['长上下文', '最新版本'], isNew: true },
    { id: '22', name: 'claude-3-5-haiku-20241022', modelId: 'claude-3-5-haiku-20241022', provider: 'Anthropic', category: '文本生成', inputPrice: 'Input: $2 / M tokens', outputPrice: 'Output: $10 / M tokens', description: 'Claude 3.5 Haiku版本', features: ['快速响应', '经济实惠'] },
    { id: '23', name: 'claude-3-haiku-20240307', modelId: 'claude-3-haiku-20240307', provider: 'Anthropic', category: '文本生成', inputPrice: 'Input: $0.5 / M tokens', outputPrice: 'Output: $2.5 / M tokens', description: 'Claude 3 Haiku版本', features: ['超快响应', '经济实惠'], isRecommended: true },
    { id: '24', name: 'claude-3-sonnet-20240229', modelId: 'claude-3-sonnet-20240229', provider: 'Anthropic', category: '文本生成', inputPrice: 'Input: $18 / M tokens', outputPrice: 'Output: $90 / M tokens', description: 'Claude 3 Sonnet版本', features: ['平衡性能', '长上下文'] },
    { id: '25', name: 'claude-3-opus-20240229', modelId: 'claude-3-opus-20240229', provider: 'Anthropic', category: '文本生成', inputPrice: 'Input: $30 / M tokens', outputPrice: 'Output: $150 / M tokens', description: 'Claude 3 Opus旗舰版本', features: ['顶级性能', '复杂推理'] },
    { id: '26', name: 'claude-opus-4-20250514', modelId: 'claude-opus-4-20250514', provider: 'Anthropic', category: '文本生成', inputPrice: 'Input: $30 / M tokens', outputPrice: 'Output: $150 / M tokens', description: 'Claude Opus 4版本', features: ['最新旗舰', '顶级性能'], isNew: true },
    { id: '27', name: 'claude-sonnet-4-20250514', modelId: 'claude-sonnet-4-20250514', provider: 'Anthropic', category: '文本生成', inputPrice: 'Input: $6 / M tokens', outputPrice: 'Output: $30 / M tokens', description: 'Claude Sonnet 4版本', features: ['最新版本', '平衡性能'], isNew: true },
    { id: '28', name: 'claude-3-7-sonnet-20250219', modelId: 'claude-3-7-sonnet-20250219', provider: 'Anthropic', category: '文本生成', inputPrice: 'Input: $6 / M tokens', outputPrice: 'Output: $30 / M tokens', description: 'Claude 3.7 Sonnet版本', features: ['升级版本', '性能提升'], isNew: true },

    // Google 模型
    { id: '29', name: 'gemini-2.0-flash-exp', modelId: 'gemini-2.0-flash-exp', provider: 'Google', category: '文本生成', inputPrice: 'Input: $1.2 / M tokens', outputPrice: 'Output: $4.8 / M tokens', description: 'Gemini 2.0 Flash实验版', features: ['最新版本', '快速响应'], isNew: true },
    { id: '30', name: 'gemini-2.0-flash-thinking-exp-1219', modelId: 'gemini-2.0-flash-thinking-exp-1219', provider: 'Google', category: '文本生成', inputPrice: 'Input: $1.2 / M tokens', outputPrice: 'Output: $4.8 / M tokens', description: 'Gemini 2.0 Flash思维版', features: ['思维链', '推理能力'], isNew: true },
    { id: '31', name: 'gemini-exp-1206', modelId: 'gemini-exp-1206', provider: 'Google', category: '文本生成', inputPrice: 'Input: $4 / M tokens', outputPrice: 'Output: $16 / M tokens', description: 'Gemini实验版12月', features: ['实验功能', '高性能'] },
    { id: '32', name: 'gemini-1.5-pro', modelId: 'gemini-1.5-pro', provider: 'Google', category: '文本生成', inputPrice: 'Input: $7 / M tokens', outputPrice: 'Output: $21 / M tokens', description: 'Gemini 1.5 Pro版本', features: ['长上下文', '多模态'], isRecommended: true },
    { id: '33', name: 'gemini-1.5-flash', modelId: 'gemini-1.5-flash', provider: 'Google', category: '文本生成', inputPrice: 'Input: $0.7 / M tokens', outputPrice: 'Output: $2.1 / M tokens', description: 'Gemini 1.5 Flash版本', features: ['快速响应', '经济实惠'], isRecommended: true },
    { id: '34', name: 'gemini-2.5-pro-preview-06-05', modelId: 'gemini-2.5-pro-preview-06-05', provider: 'Google', category: '文本生成', inputPrice: 'Input: $2.5 / M tokens', outputPrice: 'Output: $12.5 / M tokens', description: 'Gemini 2.5 Pro预览版', features: ['预览版本', '高性能'], isNew: true },
    { id: '35', name: 'gemini-2.5-flash-preview-04-17', modelId: 'gemini-2.5-flash-preview-04-17', provider: 'Google', category: '文本生成', inputPrice: 'Input: $0.3 / M tokens', outputPrice: 'Output: $1.2 / M tokens', description: 'Gemini 2.5 Flash预览版', features: ['预览版本', '超快响应'], isNew: true },
    { id: '36', name: 'veo3', modelId: 'veo3', provider: 'Google', category: '视频生成', inputPrice: 'Price: $2 / 次', outputPrice: '', description: 'Veo3视频生成模型', features: ['视频生成', '按次计费'], isNew: true },
    { id: '37', name: 'veo3-pro', modelId: 'veo3-pro', provider: 'Google', category: '视频生成', inputPrice: 'Price: $10 / 次', outputPrice: '', description: 'Veo3专业版视频生成', features: ['专业级', '高质量视频'], isNew: true },

    // DeepSeek 模型
    { id: '38', name: 'deepseek-chat', modelId: 'deepseek-chat', provider: 'DeepSeek', category: '文本生成', inputPrice: 'Input: $1 / M tokens', outputPrice: 'Output: $4 / M tokens', description: 'DeepSeek聊天模型', features: ['中文优化', '经济实惠'], isRecommended: true },
    { id: '39', name: 'deepseek-coder', modelId: 'deepseek-coder', provider: 'DeepSeek', category: '代码生成', inputPrice: 'Input: $1 / M tokens', outputPrice: 'Output: $4 / M tokens', description: 'DeepSeek代码生成模型', features: ['代码专家', '编程助手'] },
    { id: '40', name: 'deepseek-reasoner', modelId: 'deepseek-reasoner', provider: 'DeepSeek', category: '推理模型', inputPrice: 'Input: $1.65 / M tokens', outputPrice: 'Output: $6.6 / M tokens', description: 'DeepSeek推理模型', features: ['推理能力', '逻辑分析'] },
    { id: '41', name: 'deepseek-v3-all', modelId: 'deepseek-v3-all', provider: 'DeepSeek', category: '文本生成', inputPrice: 'Input: $0.81 / M tokens', outputPrice: 'Output: $3.24 / M tokens', description: 'DeepSeek V3全功能版', features: ['全功能', '性价比高'], isRecommended: true },
    { id: '42', name: 'deepseek-r1', modelId: 'deepseek-r1', provider: 'DeepSeek', category: '推理模型', inputPrice: 'Input: $1.65 / M tokens', outputPrice: 'Output: $6.6 / M tokens', description: 'DeepSeek R1推理模型', features: ['推理专家', '数学能力'], isNew: true },
    { id: '43', name: 'deepseek-r1-0528', modelId: 'deepseek-r1-0528', provider: 'DeepSeek', category: '推理模型', inputPrice: 'Input: $1.65 / M tokens', outputPrice: 'Output: $6.6 / M tokens', description: 'DeepSeek R1 5月版本', features: ['推理专家', '特定版本'] },
    { id: '44', name: 'deepseek-v3-0324', modelId: 'deepseek-v3-0324', provider: 'DeepSeek', category: '文本生成', inputPrice: 'Input: $1 / M tokens', outputPrice: 'Output: $4 / M tokens', description: 'DeepSeek V3 3月版本', features: ['稳定版本', '性价比高'] },

    // xAI 模型
    { id: '45', name: 'grok-2-1212', modelId: 'grok-2-1212', provider: 'xAI', category: '文本生成', inputPrice: 'Input: $2 / M tokens', outputPrice: 'Output: $10 / M tokens', description: 'Grok 2.0 12月版本', features: ['实时信息', '幽默风格'] },
    { id: '46', name: 'grok-beta', modelId: 'grok-beta', provider: 'xAI', category: '文本生成', inputPrice: 'Input: $2 / M tokens', outputPrice: 'Output: $6 / M tokens', description: 'Grok测试版本', features: ['测试版本', '新功能'] },
    { id: '47', name: 'grok-3', modelId: 'grok-3', provider: 'xAI', category: '文本生成', inputPrice: 'Input: $2 / M tokens', outputPrice: 'Output: $10 / M tokens', description: 'Grok 3.0版本', features: ['最新版本', '增强能力'], isNew: true },
    { id: '48', name: 'grok-3-reasoner', modelId: 'grok-3-reasoner', provider: 'xAI', category: '推理模型', inputPrice: 'Input: $2 / M tokens', outputPrice: 'Output: $10 / M tokens', description: 'Grok 3推理版本', features: ['推理能力', '逻辑分析'], isNew: true },
    { id: '49', name: 'grok-3-deepsearch', modelId: 'grok-3-deepsearch', provider: 'xAI', category: '搜索模型', inputPrice: 'Input: $2 / M tokens', outputPrice: 'Output: $2 / M tokens', description: 'Grok 3深度搜索版', features: ['深度搜索', '信息检索'], isNew: true }
  ];

  const providers = ['OpenAI', 'Anthropic', 'Google', 'DeepSeek', 'xAI'];

  const filteredModels = models.filter(model => {
    const matchesSearch = model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         model.modelId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesProvider = model.provider === selectedProvider;
    
    return matchesSearch && matchesProvider;
  });

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const downloadModelList = () => {
    // 创建下载链接
    const link = document.createElement('a');
    link.href = '/模型ID清单与费率(持续更新).pdf';
    link.download = '模型ID清单与费率(持续更新).pdf';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getProviderColor = (provider: string) => {
    const colors: Record<string, string> = {
      'OpenAI': 'bg-green-100 text-green-800',
      'Anthropic': 'bg-orange-100 text-orange-800',
      'Google': 'bg-blue-100 text-blue-800',
      'DeepSeek': 'bg-purple-100 text-purple-800',
      'xAI': 'bg-indigo-100 text-indigo-800'
    };
    return colors[provider] || 'bg-gray-100 text-gray-800';
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      '文本生成': 'bg-blue-50 text-blue-700',
      '图像生成': 'bg-pink-50 text-pink-700',
      '视频生成': 'bg-green-50 text-green-700',
      '推理模型': 'bg-yellow-50 text-yellow-700',
      '代码生成': 'bg-purple-50 text-purple-700',
      '搜索模型': 'bg-cyan-50 text-cyan-700'
    };
    return colors[category] || 'bg-gray-50 text-gray-700';
  };

  const getProviderStats = (provider: string) => {
    const providerModels = models.filter(m => m.provider === provider);
    return {
      total: providerModels.length,
      recommended: providerModels.filter(m => m.isRecommended).length,
      new: providerModels.filter(m => m.isNew).length
    };
  };

  return (
    <div className="w-full">
      {/* Content */}
      <div className="w-full">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">模型管理</h1>
                <p className="text-gray-600 mt-2">管理和监控您的AI模型</p>
              </div>
            </div>
          </div>

          {/* Recommended Models List */}
          <div className="bg-white border border-gray-200 rounded-xl p-4 mb-6">
            <div className="flex items-center mb-3">
              <div className="w-6 h-6 bg-yellow-100 rounded-lg flex items-center justify-center mr-2">
                <Star className="w-3 h-3 text-yellow-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900">🔥🔥🔥推荐模型清单</h4>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-2 px-3 font-medium text-gray-900 bg-gray-50 text-sm">模型</th>
                    <th className="text-left py-2 px-3 font-medium text-gray-900 bg-gray-50 text-sm">模型 ID</th>
                    <th className="text-left py-2 px-3 font-medium text-gray-900 bg-gray-50 text-sm">备注</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-2 px-3 font-medium text-gray-900 text-sm">Claude</td>
                    <td className="py-2 px-3 text-gray-700 font-mono text-xs">
                      <div className="space-y-0.5">
                        <div>claude-sonnet-4-20250514</div>
                        <div>claude-3-7-sonnet-20250219</div>
                        <div>claude-opus-4-20250514</div>
                        <div>claude-sonnet-4-20250514-thinking</div>
                      </div>
                    </td>
                    <td className="py-2 px-3 text-gray-600 text-sm">编程、推理</td>
                  </tr>
                  <tr className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-2 px-3 font-medium text-gray-900 text-sm">deepseek</td>
                    <td className="py-2 px-3 text-gray-700 font-mono text-xs">
                      <div className="space-y-0.5">
                        <div>deepseek-r1-0528</div>
                        <div>deepseek-v3-0324</div>
                      </div>
                    </td>
                    <td className="py-2 px-3 text-gray-600 text-sm">性价比之王</td>
                  </tr>
                  <tr className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-2 px-3 font-medium text-gray-900 text-sm">gemini</td>
                    <td className="py-2 px-3 text-gray-700 font-mono text-xs">
                      <div className="space-y-0.5">
                        <div>gemini-2.5-pro-preview-06-05</div>
                        <div>gemini-2.5-flash-preview-04-17</div>
                      </div>
                    </td>
                    <td className="py-2 px-3 text-gray-600 text-sm">写作</td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="py-2 px-3 font-medium text-gray-900 text-sm">chatgpt</td>
                    <td className="py-2 px-3 text-gray-700 font-mono text-xs">
                      <div className="space-y-0.5">
                        <div>gpt-4o</div>
                        <div>gpt-4.1</div>
                        <div>gpt-4o-mini</div>
                      </div>
                    </td>
                    <td className="py-2 px-3 text-gray-600 text-sm">推理、写作</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-xs text-yellow-800">
                <strong>提示：</strong>以上模型ID可直接复制使用，建议根据具体需求选择合适的模型。
              </p>
            </div>
          </div>

          {/* Provider Filter */}
          <div className="mb-6">
            <div className="bg-white border border-gray-200 rounded-xl p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Building2 className="w-5 h-5 mr-2" />
                选择供应商
              </h3>
              <div className="grid grid-cols-2 md:flex md:flex-wrap gap-2">
                {providers.map((provider) => {
                  const stats = getProviderStats(provider);
                  return (
                    <button
                      key={provider}
                      onClick={() => setSelectedProvider(provider)}
                      className={`flex flex-col md:flex-row items-center justify-between px-3 py-2 md:px-4 rounded-lg text-sm font-medium transition-colors ${
                        selectedProvider === provider
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <span className="text-center md:text-left">{provider}</span>
                      </div>
                      <div className="flex items-center space-x-1 mt-1 md:mt-0">
                        <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                          {stats.total}
                        </span>
                        {stats.new > 0 && (
                          <span className="text-xs bg-green-200 text-green-600 px-2 py-1 rounded-full">
                            {stats.new}新
                          </span>
                        )}
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Current Provider Header */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div>
              <h2 className="text-xl md:text-2xl font-bold text-gray-900 flex flex-col md:flex-row md:items-center">
                <span className={`px-3 py-1 rounded-lg text-sm font-medium mb-2 md:mb-0 md:mr-3 inline-block ${getProviderColor(selectedProvider)}`}>
                  {selectedProvider}
                </span>
                <span>模型列表</span>
              </h2>
              <p className="text-gray-600 mt-1 text-sm md:text-base">
                共 {filteredModels.length} 个模型，
                推荐 {filteredModels.filter(m => m.isRecommended).length} 个，
                新增 {filteredModels.filter(m => m.isNew).length} 个
              </p>
            </div>

            <div className="flex flex-col md:flex-row items-stretch md:items-center space-y-3 md:space-y-0 md:space-x-4">
              <div className="relative">
                <Search className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                <input
                  type="text"
                  placeholder="搜索模型..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full md:w-auto pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Download Button */}
              <button
                onClick={downloadModelList}
                className="flex items-center justify-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
              >
                <Download className="w-5 h-5" />
                <span className="hidden md:inline">下载完整列表</span>
                <span className="md:hidden">下载</span>
              </button>
            </div>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 md:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">总模型数</p>
                  <p className="text-2xl md:text-3xl font-bold text-gray-900">{filteredModels.length}</p>
                </div>
                <div className="w-10 h-10 md:w-12 md:h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 md:w-6 md:h-6 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 md:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">推荐模型</p>
                  <p className="text-2xl md:text-3xl font-bold text-gray-900">
                    {filteredModels.filter(m => m.isRecommended).length}
                  </p>
                </div>
                <div className="w-10 h-10 md:w-12 md:h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <Star className="w-5 h-5 md:w-6 md:h-6 text-yellow-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 md:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">新增模型</p>
                  <p className="text-2xl md:text-3xl font-bold text-gray-900">
                    {filteredModels.filter(m => m.isNew).length}
                  </p>
                </div>
                <div className="w-10 h-10 md:w-12 md:h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 md:w-6 md:h-6 text-green-600" />
                </div>
              </div>
            </div>
          </div>

          {/* Models Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredModels.map((model) => (
              <div key={model.id} className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                <div className="p-6">
                  {/* Model Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{model.name}</h3>
                        {model.isRecommended && (
                          <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full flex items-center">
                            <Star className="w-3 h-3 mr-1" />
                            推荐
                          </span>
                        )}
                        {model.isNew && (
                          <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                            新品
                          </span>
                        )}
                        {model.isPopular && (
                          <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                            热门
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-2 mb-2">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getCategoryColor(model.category)}`}>
                          {model.category}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Model ID */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                      <code className="text-sm font-mono text-gray-800">{model.modelId}</code>
                      <button
                        onClick={() => copyToClipboard(model.modelId)}
                        className="text-gray-400 hover:text-gray-600 transition-colors"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  {/* Pricing */}
                  <div className="mb-4">
                    <div className="space-y-2">
                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <p className="text-xs text-blue-600 mb-1">输入价格</p>
                        <p className="text-sm font-semibold text-blue-900">
                          {model.inputPrice}
                        </p>
                      </div>
                      {model.outputPrice && (
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <p className="text-xs text-green-600 mb-1">输出价格</p>
                          <p className="text-sm font-semibold text-green-900">
                            {model.outputPrice}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Context Length */}
                  {model.contextLength && (
                    <div className="mb-4">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">上下文长度:</span>
                        <span className="font-medium text-gray-900">
                          {model.contextLength.toLocaleString()} tokens
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Description */}
                  <div className="mb-4">
                    <p className="text-sm text-gray-600">{model.description}</p>
                  </div>

                  {/* Features */}
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-1">
                      {model.features.map((feature, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                    <button
                      onClick={() => copyToClipboard(model.modelId)}
                      className="flex items-center space-x-1 px-3 py-1.5 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
                    >
                      <Copy className="w-4 h-4" />
                      <span>复制ID</span>
                    </button>
                    
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <DollarSign className="w-3 h-3" />
                      <span>实时价格</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Empty State */}
          {filteredModels.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="w-12 h-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的模型</h3>
              <p className="text-gray-600">请尝试调整搜索条件。</p>
            </div>
          )}

          {/* Footer Note */}
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <ExternalLink className="w-4 h-4 text-blue-600" />
                </div>
              </div>
              <div className="ml-4">
                <h4 className="text-lg font-semibold text-blue-900 mb-2">使用说明</h4>
                <div className="text-blue-800 text-sm space-y-2">
                  <p>• <strong>价格格式：</strong>严格按照供应商官方定价格式显示，如 "Input: $0.15 / M tokens"</p>
                  <p>• <strong>模型ID：</strong>所有模型ID均为官方标准格式，可直接复制使用</p>
                  <p>• <strong>供应商分类：</strong>点击上方供应商按钮查看对应的所有模型</p>
                  <p>• <strong>完整列表：</strong>点击"下载完整列表"获取包含所有模型的PDF文档</p>
                  <p>• <strong>实时更新：</strong>模型列表和价格信息根据供应商变化及时更新</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModelManagement;