// SEO工具函数
import { siteInfo } from '../config/seo';

// 生成页面URL
export const generatePageUrl = (path: string): string => {
  const baseUrl = `https://${siteInfo.domain}`;
  return path.startsWith('/') ? `${baseUrl}${path}` : `${baseUrl}/${path}`;
};

// 验证SEO标签是否存在
export const validateSEOTags = (): { [key: string]: boolean } => {
  const results: { [key: string]: boolean } = {};
  
  // 检查基础标签
  results.title = !!document.title;
  results.description = !!document.querySelector('meta[name="description"]');
  results.keywords = !!document.querySelector('meta[name="keywords"]');
  results.canonical = !!document.querySelector('link[rel="canonical"]');
  
  // 检查Open Graph标签
  results.ogTitle = !!document.querySelector('meta[property="og:title"]');
  results.ogDescription = !!document.querySelector('meta[property="og:description"]');
  results.ogImage = !!document.querySelector('meta[property="og:image"]');
  results.ogUrl = !!document.querySelector('meta[property="og:url"]');
  
  // 检查Twitter标签
  results.twitterCard = !!document.querySelector('meta[property="twitter:card"]');
  results.twitterTitle = !!document.querySelector('meta[property="twitter:title"]');
  results.twitterDescription = !!document.querySelector('meta[property="twitter:description"]');
  
  return results;
};

// 获取页面SEO信息
export const getPageSEOInfo = () => {
  const title = document.title;
  const description = document.querySelector('meta[name="description"]')?.getAttribute('content') || '';
  const keywords = document.querySelector('meta[name="keywords"]')?.getAttribute('content') || '';
  const canonical = document.querySelector('link[rel="canonical"]')?.getAttribute('href') || '';
  
  return {
    title,
    description,
    keywords,
    canonical,
    titleLength: title.length,
    descriptionLength: description.length,
    keywordsCount: keywords.split(',').filter(k => k.trim()).length
  };
};

// SEO建议
export const getSEOSuggestions = () => {
  const info = getPageSEOInfo();
  const suggestions: string[] = [];
  
  // 标题长度建议
  if (info.titleLength < 30) {
    suggestions.push('页面标题过短，建议30-60个字符');
  } else if (info.titleLength > 60) {
    suggestions.push('页面标题过长，建议30-60个字符');
  }
  
  // 描述长度建议
  if (info.descriptionLength < 120) {
    suggestions.push('页面描述过短，建议120-160个字符');
  } else if (info.descriptionLength > 160) {
    suggestions.push('页面描述过长，建议120-160个字符');
  }
  
  // 关键词建议
  if (info.keywordsCount < 3) {
    suggestions.push('关键词数量过少，建议3-10个关键词');
  } else if (info.keywordsCount > 10) {
    suggestions.push('关键词数量过多，建议3-10个关键词');
  }
  
  // Canonical URL建议
  if (!info.canonical) {
    suggestions.push('缺少canonical URL，建议添加');
  }
  
  return suggestions;
};

// 生成结构化数据
export const generateStructuredData = (type: string, data: any) => {
  const baseStructure = {
    "@context": "https://schema.org",
    "@type": type,
    ...data
  };
  
  return JSON.stringify(baseStructure, null, 2);
};

// 检查页面加载性能（SEO相关）
export const checkPagePerformance = (): Promise<any> => {
  return new Promise((resolve) => {
    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const paint = performance.getEntriesByType('paint');
      
      const metrics = {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
        firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0
      };
      
      resolve(metrics);
    } else {
      resolve(null);
    }
  });
};

// 生成面包屑导航结构化数据
export const generateBreadcrumbStructuredData = (breadcrumbs: Array<{name: string, url: string}>) => {
  const itemListElement = breadcrumbs.map((crumb, index) => ({
    "@type": "ListItem",
    "position": index + 1,
    "name": crumb.name,
    "item": crumb.url
  }));
  
  return generateStructuredData("BreadcrumbList", {
    itemListElement
  });
};

// 检查图片SEO优化
export const checkImageSEO = () => {
  const images = document.querySelectorAll('img');
  const issues: string[] = [];
  
  images.forEach((img, index) => {
    if (!img.alt) {
      issues.push(`图片 ${index + 1} 缺少alt属性`);
    }
    if (!img.title && img.alt) {
      issues.push(`图片 ${index + 1} 建议添加title属性`);
    }
  });
  
  return issues;
};

// 检查链接SEO优化
export const checkLinkSEO = () => {
  const links = document.querySelectorAll('a[href]');
  const issues: string[] = [];
  
  links.forEach((link, index) => {
    const href = link.getAttribute('href');
    const target = link.getAttribute('target');
    const rel = link.getAttribute('rel');
    
    // 检查外部链接
    if (href && (href.startsWith('http') && !href.includes(siteInfo.domain))) {
      if (target === '_blank' && !rel?.includes('noopener')) {
        issues.push(`外部链接 ${index + 1} 建议添加 rel="noopener noreferrer"`);
      }
    }
    
    // 检查链接文本
    if (!link.textContent?.trim()) {
      issues.push(`链接 ${index + 1} 缺少描述性文本`);
    }
  });
  
  return issues;
};
