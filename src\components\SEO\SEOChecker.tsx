import React, { useState, useEffect } from 'react';
import { CheckCircle, AlertTriangle, XCircle, Eye, EyeOff } from 'lucide-react';
import { 
  validateSEOTags, 
  getPageSEOInfo, 
  getSEOSuggestions,
  checkImageSEO,
  checkLinkSEO 
} from '../../utils/seoUtils';

interface SEOCheckResult {
  category: string;
  checks: Array<{
    name: string;
    status: 'pass' | 'warning' | 'fail';
    message: string;
  }>;
}

const SEOChecker: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [results, setResults] = useState<SEOCheckResult[]>([]);
  const [loading, setLoading] = useState(false);

  const runSEOCheck = async () => {
    setLoading(true);
    
    // 基础SEO检查
    const seoTags = validateSEOTags();
    const pageInfo = getPageSEOInfo();
    const suggestions = getSEOSuggestions();
    const imageIssues = checkImageSEO();
    const linkIssues = checkLinkSEO();

    const checkResults: SEOCheckResult[] = [
      {
        category: '基础SEO标签',
        checks: [
          {
            name: '页面标题',
            status: seoTags.title ? 'pass' : 'fail',
            message: seoTags.title ? `标题长度: ${pageInfo.titleLength}字符` : '缺少页面标题'
          },
          {
            name: 'Meta描述',
            status: seoTags.description ? 'pass' : 'fail',
            message: seoTags.description ? `描述长度: ${pageInfo.descriptionLength}字符` : '缺少Meta描述'
          },
          {
            name: '关键词标签',
            status: seoTags.keywords ? 'pass' : 'warning',
            message: seoTags.keywords ? `关键词数量: ${pageInfo.keywordsCount}个` : '缺少关键词标签'
          },
          {
            name: 'Canonical URL',
            status: seoTags.canonical ? 'pass' : 'warning',
            message: seoTags.canonical ? '已设置Canonical URL' : '建议添加Canonical URL'
          }
        ]
      },
      {
        category: 'Open Graph标签',
        checks: [
          {
            name: 'OG标题',
            status: seoTags.ogTitle ? 'pass' : 'fail',
            message: seoTags.ogTitle ? '已设置OG标题' : '缺少OG标题'
          },
          {
            name: 'OG描述',
            status: seoTags.ogDescription ? 'pass' : 'fail',
            message: seoTags.ogDescription ? '已设置OG描述' : '缺少OG描述'
          },
          {
            name: 'OG图片',
            status: seoTags.ogImage ? 'pass' : 'warning',
            message: seoTags.ogImage ? '已设置OG图片' : '建议添加OG图片'
          },
          {
            name: 'OG URL',
            status: seoTags.ogUrl ? 'pass' : 'warning',
            message: seoTags.ogUrl ? '已设置OG URL' : '建议添加OG URL'
          }
        ]
      },
      {
        category: 'Twitter标签',
        checks: [
          {
            name: 'Twitter卡片',
            status: seoTags.twitterCard ? 'pass' : 'warning',
            message: seoTags.twitterCard ? '已设置Twitter卡片' : '建议添加Twitter卡片'
          },
          {
            name: 'Twitter标题',
            status: seoTags.twitterTitle ? 'pass' : 'warning',
            message: seoTags.twitterTitle ? '已设置Twitter标题' : '建议添加Twitter标题'
          },
          {
            name: 'Twitter描述',
            status: seoTags.twitterDescription ? 'pass' : 'warning',
            message: seoTags.twitterDescription ? '已设置Twitter描述' : '建议添加Twitter描述'
          }
        ]
      },
      {
        category: '内容优化',
        checks: [
          {
            name: '标题长度',
            status: pageInfo.titleLength >= 30 && pageInfo.titleLength <= 60 ? 'pass' : 'warning',
            message: `当前${pageInfo.titleLength}字符，建议30-60字符`
          },
          {
            name: '描述长度',
            status: pageInfo.descriptionLength >= 120 && pageInfo.descriptionLength <= 160 ? 'pass' : 'warning',
            message: `当前${pageInfo.descriptionLength}字符，建议120-160字符`
          },
          {
            name: '关键词数量',
            status: pageInfo.keywordsCount >= 3 && pageInfo.keywordsCount <= 10 ? 'pass' : 'warning',
            message: `当前${pageInfo.keywordsCount}个，建议3-10个`
          }
        ]
      }
    ];

    // 添加图片和链接检查
    if (imageIssues.length > 0) {
      checkResults.push({
        category: '图片SEO',
        checks: imageIssues.map(issue => ({
          name: '图片优化',
          status: 'warning' as const,
          message: issue
        }))
      });
    }

    if (linkIssues.length > 0) {
      checkResults.push({
        category: '链接SEO',
        checks: linkIssues.map(issue => ({
          name: '链接优化',
          status: 'warning' as const,
          message: issue
        }))
      });
    }

    setResults(checkResults);
    setLoading(false);
  };

  useEffect(() => {
    if (isVisible) {
      runSEOCheck();
    }
  }, [isVisible]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'fail':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass':
        return 'text-green-700 bg-green-50';
      case 'warning':
        return 'text-yellow-700 bg-yellow-50';
      case 'fail':
        return 'text-red-700 bg-red-50';
      default:
        return 'text-gray-700 bg-gray-50';
    }
  };

  // 只在开发环境显示
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <>
      {/* 切换按钮 */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 z-50 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
        title="SEO检查器"
      >
        {isVisible ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
      </button>

      {/* SEO检查面板 */}
      {isVisible && (
        <div className="fixed bottom-20 right-4 z-40 w-96 max-h-96 bg-white border border-gray-200 rounded-lg shadow-xl overflow-hidden">
          <div className="bg-blue-600 text-white p-4 flex items-center justify-between">
            <h3 className="font-semibold">SEO检查器</h3>
            <button
              onClick={runSEOCheck}
              disabled={loading}
              className="text-blue-100 hover:text-white transition-colors"
            >
              {loading ? '检查中...' : '重新检查'}
            </button>
          </div>
          
          <div className="max-h-80 overflow-y-auto p-4 space-y-4">
            {results.map((category, categoryIndex) => (
              <div key={categoryIndex}>
                <h4 className="font-medium text-gray-900 mb-2">{category.category}</h4>
                <div className="space-y-2">
                  {category.checks.map((check, checkIndex) => (
                    <div
                      key={checkIndex}
                      className={`p-2 rounded-md ${getStatusColor(check.status)}`}
                    >
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(check.status)}
                        <span className="text-sm font-medium">{check.name}</span>
                      </div>
                      <p className="text-xs mt-1 opacity-80">{check.message}</p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
            
            {results.length === 0 && !loading && (
              <p className="text-gray-500 text-center py-4">点击"重新检查"开始SEO检查</p>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default SEOChecker;
