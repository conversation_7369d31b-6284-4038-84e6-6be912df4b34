<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🤖</text></svg>" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- 基础SEO信息 -->
    <title>AI模型套餐平台 - OpenAI、Claude、DeepSeek等AI模型API服务</title>
    <meta name="description" content="专业的AI模型API服务平台，提供OpenAI GPT-4、<PERSON>、DeepSeek、Gemini等主流AI模型套餐。支持沉浸式翻译、Cherry Studio等多种客户端，价格实惠，最低0.66折优惠。" />
    <meta name="keywords" content="AI模型,OpenAI,GPT-4,<PERSON>,DeepSeek,API服务,AI套餐,沉浸式翻译,Cherry Studio,ChatBox,人工智能,机器学习,API密钥,AI工具" />
    <meta name="author" content="AI模型套餐平台" />
    <meta name="robots" content="index, follow" />
    <meta name="language" content="zh-CN" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://your-domain.com/" />
    <meta property="og:title" content="AI模型套餐平台 - OpenAI、Claude、DeepSeek等AI模型API服务" />
    <meta property="og:description" content="专业的AI模型API服务平台，提供OpenAI GPT-4、Claude、DeepSeek、Gemini等主流AI模型套餐。支持沉浸式翻译、Cherry Studio等多种客户端，价格实惠，最低0.66折优惠。" />
    <meta property="og:image" content="https://your-domain.com/og-image.png" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:locale" content="zh_CN" />
    <meta property="og:site_name" content="AI模型套餐平台" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://your-domain.com/" />
    <meta property="twitter:title" content="AI模型套餐平台 - OpenAI、Claude、DeepSeek等AI模型API服务" />
    <meta property="twitter:description" content="专业的AI模型API服务平台，提供OpenAI GPT-4、Claude、DeepSeek、Gemini等主流AI模型套餐。支持沉浸式翻译、Cherry Studio等多种客户端，价格实惠，最低0.66折优惠。" />
    <meta property="twitter:image" content="https://your-domain.com/og-image.png" />

    <!-- 其他SEO标签 -->
    <meta name="theme-color" content="#667eea" />
    <meta name="msapplication-TileColor" content="#667eea" />
    <meta name="application-name" content="AI模型套餐平台" />
    <meta name="apple-mobile-web-app-title" content="AI模型套餐平台" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />

    <!-- 结构化数据 -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "AI模型套餐平台",
      "description": "专业的AI模型API服务平台，提供OpenAI GPT-4、Claude、DeepSeek、Gemini等主流AI模型套餐",
      "url": "https://your-domain.com",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://your-domain.com/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      },
      "publisher": {
        "@type": "Organization",
        "name": "AI模型套餐平台",
        "logo": {
          "@type": "ImageObject",
          "url": "https://your-domain.com/logo.png"
        }
      }
    }
    </script>

    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Product",
      "name": "AI模型API套餐服务",
      "description": "提供OpenAI GPT-4、Claude、DeepSeek等主流AI模型的API服务套餐",
      "brand": {
        "@type": "Brand",
        "name": "AI模型套餐平台"
      },
      "offers": {
        "@type": "AggregateOffer",
        "priceCurrency": "CNY",
        "lowPrice": "0.8",
        "highPrice": "40",
        "offerCount": "6"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "reviewCount": "150"
      }
    }
    </script>
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 加载指示器样式 -->
    <style>
      #loading-indicator {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        color: white;
      }
      
      .loading-logo {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 40px;
        margin-bottom: 24px;
        animation: pulse 2s ease-in-out infinite;
      }
      
      .loading-text {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 12px;
        text-align: center;
      }
      
      .loading-subtitle {
        font-size: 16px;
        opacity: 0.8;
        margin-bottom: 32px;
        text-align: center;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-dots {
        display: flex;
        gap: 8px;
        margin-top: 16px;
      }
      
      .loading-dot {
        width: 8px;
        height: 8px;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 50%;
        animation: bounce 1.4s ease-in-out infinite both;
      }
      
      .loading-dot:nth-child(1) { animation-delay: -0.32s; }
      .loading-dot:nth-child(2) { animation-delay: -0.16s; }
      .loading-dot:nth-child(3) { animation-delay: 0s; }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      @keyframes pulse {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.05); opacity: 0.8; }
      }
      
      @keyframes bounce {
        0%, 80%, 100% { transform: scale(0); }
        40% { transform: scale(1); }
      }
      
      /* 响应式设计 */
      @media (max-width: 640px) {
        .loading-logo {
          width: 60px;
          height: 60px;
          font-size: 30px;
          margin-bottom: 20px;
        }
        
        .loading-text {
          font-size: 20px;
        }
        
        .loading-subtitle {
          font-size: 14px;
          margin-bottom: 24px;
        }
        
        .loading-spinner {
          width: 32px;
          height: 32px;
        }
      }
      
      /* 隐藏加载指示器的动画 */
      .loading-fade-out {
        opacity: 0;
        transition: opacity 0.5s ease-out;
        pointer-events: none;
      }
    </style>
  </head>
  <body>
    <!-- 加载指示器 -->
    <div id="loading-indicator">
      <div class="loading-logo">🤖</div>
      <div class="loading-text">模型中心</div>
      <div class="loading-subtitle">AI管理平台正在加载...</div>
      <div class="loading-spinner"></div>
      <div class="loading-dots">
        <div class="loading-dot"></div>
        <div class="loading-dot"></div>
        <div class="loading-dot"></div>
      </div>
    </div>
    
    <!-- React 应用根节点 -->
    <div id="root"></div>
    
    <!-- 应用脚本 -->
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- 隐藏加载指示器的脚本 -->
    <script>
      // 当 React 应用加载完成后隐藏加载指示器
      window.addEventListener('load', function() {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
          // 添加淡出动画
          loadingIndicator.classList.add('loading-fade-out');
          // 动画完成后移除元素
          setTimeout(() => {
            if (loadingIndicator.parentNode) {
              loadingIndicator.parentNode.removeChild(loadingIndicator);
            }
          }, 500);
        }
      });
      
      // 备用方案：如果 React 应用在 5 秒内没有加载完成，也隐藏加载指示器
      setTimeout(function() {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
          loadingIndicator.classList.add('loading-fade-out');
          setTimeout(() => {
            if (loadingIndicator.parentNode) {
              loadingIndicator.parentNode.removeChild(loadingIndicator);
            }
          }, 500);
        }
      }, 5000);
    </script>
  </body>
</html>