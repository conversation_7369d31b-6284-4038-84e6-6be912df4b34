{"name": "ai-model-management-platform", "private": true, "version": "1.0.0", "type": "module", "description": "专业的AI模型API服务平台，提供OpenAI GPT-4、<PERSON>、DeepSeek、Gemini等主流AI模型套餐。支持沉浸式翻译、Cherry Studio等多种客户端，价格实惠，最低0.66折优惠。", "keywords": ["AI模型", "OpenAI", "GPT-4", "<PERSON>", "DeepSeek", "API服务", "AI套餐", "沉浸式翻译", "Cherry Studio", "ChatBox", "人工智能", "机器学习", "API密钥", "AI工具"], "author": "AI模型套餐平台", "homepage": "https://your-domain.com", "repository": {"type": "git", "url": "https://github.com/your-username/ai-model-platform.git"}, "bugs": {"url": "https://github.com/your-username/ai-model-platform/issues"}, "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}, "engines": {"node": ">=18.0.0"}}