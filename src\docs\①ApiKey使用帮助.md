## 一、闲鱼店铺

![[Pasted image 20250612105817.png]]

|                 类别                  |   套餐名称   | 额度  |  折扣  |  实付金额  |                    备注                    |
|:---------------------------------: |:------: |:-: |:--: |:----: |:--------------------------------------: |
|               AI 全模型                | 1 刀体验套餐  | $1  | 0.9  |  0.9元  |                                          |
|                AI全模型                |  10刀套餐   | $10 | 0.9  |  9 元   |                                          |
| <font color="#00b0f0">AI 全模型</font> |  20 刀套餐  | $20 | 0.85 |  17元   |    👍<font color="#ff0000">推荐</font>     |
| <font color="#00b050">AI全模型</font>  |  50刀套餐   | $50 | 0.8  |  40 元  |   💯<font color="#ff0000">优惠力度大</font>   |
|             DeepSeek 专属             | 1 元体验套餐  | ￥1  | 0.8  | 0.8 元  |                                          |
|             DeepSeek 专属             | 10 元限时套餐 | ￥10 | 0.66 | 6.66 元 | 😘<font color="#ff0000">限时优惠，推荐体验</font> |

## 二、API 地址

https://api.sydney-ai.com

API 地址需要**客户端**配置使用，详见 " **五、使用帮助** "。

## 三、模型列表

### 1. 模型列表

  详见网盘模型 ID 大全。

### 2.推荐模型

| 模型       | 模型 ID                                                                                                        | 备注  |
| -------- | ------------------------------------------------------------------------------------------------------------ | --- |
| Claude   | claude-sonnet-4-20250514,claude-3-7-sonnet-20250219,claude-opus-4-20250514,claude-sonnet-4-20250514-thinking | 文字  |
| deepseek | deepseek-r1-0528,deepseek-v3-0324                                                                            |     |
| gemini   | gemini-2.5-pro-preview-06-05,gemini-2.5-flash-preview-04-17                                                  |     |
| chatgpt  | gpt-4o,gpt-4.1,gpt-4o-mini                                                                                   |     |

### 3.套餐说明

以上模型 ID 需要对应购买的套餐：

- **deepseek 专属 KEY**：如果你购买的是 deepseek 专属 KEY（包括 1 元体验、10 元套餐等），这么只能使用 deepseek 的 deepseek-r1-0528,deepseek-v3-0324。
- **全模型 KEY**：如果你购买的是全模型 KEY，那么以上模型列表全部可使用。

## 四、API 使用量查询

https://check.sydney-ai.com/

![[Pasted image 20250614164416.png]]

## 五、使用帮助

### 1. 沉浸式翻译插件

**进入设置：**
![[Pasted image 20250612161534.png]]
**添加服务：**
![[Pasted image 20250612161708.png]]
**详细配置：** 翻译建议使用**deepseek-v3**，经济实惠
![[PixPin_2025-06-13_23-43-18.png]]
**可以看到配置的服务：**
![[Pasted image 20250612162457.png]]
**返回页面，选择新建的服务：**
![[Pasted image 20250612162621.png]]

### 2 Cherry Studio（推荐）

https://www.cherry-ai.com/

![[Pasted image 20250612162743.png]]

![[Pasted image 20250612162942.png]]

**填写名称：**

![[Pasted image 20250612163041.png]]

**填写密钥、地址：**
![[Pasted image 20250613234642.png]]

**添加模型，支持批量添加：**
![[PixPin_2025-06-13_23-47-36.png]]

**模型检测：**
![[PixPin_2025-06-13_23-50-14.png]]
**接下来再顶部看到检测提示：**
![[PixPin_2025-06-13_23-49-15.png]]

### 3. ChatBoX

chatbox 可使用网页端和客户端，官网地址：https://chatboxai.app/zh

web 端地址：https://web.chatboxai.app/

![[PixPin_2025-06-13_23-54-11.png]]

![[Pasted image 20250612160644.png]]

**输入 key、api 地址、添加模型：**

![[PixPin_2025-06-13_23-57-55.png]]

**新建模型：**

![[Pasted image 20250612161015.png]]

**使用：**

![[Pasted image 20250612161156.png]]

### 4. SillyTavern（傻乎乎的酒馆）

> SillyTavern（傻乎乎的酒馆）是一款开源的本地 LLM（大型语言模型）前端应用，专为创意用户打造，支持多种 AI 模型和 API。它可以在 Windows、MacOS、Linux、Android（通过 Termux）等多个平台运行，提供了一个独特的交互界面，适合与虚拟角色聊天、角色扮演或创作故事。

- 地址：[https://github.com/SillyTavern/SillyTavern](https://github.com/SillyTavern/SillyTavern)  
- 一键部署教程：[https://pt4300.github.io/Sillytavern-docker-tutorial/simple_docker/](https://pt4300.github.io/Sillytavern-docker-tutorial/simple_docker/) (SillyTavern 酒馆一键部署教程 - 零基础超友好版本，按一下就能安装！)
- 根据讲解安装完毕后，进入目录，运行 start.bat（Windows）或 [start.sh](http://start.sh/)（Linux/MacOS）完成初始化
- 启动应用后，点击顶部的设置图标，进入 API 配置页面
- 在 "**API 提供方**" 中选择 **聊天补全**：**自定义 openAI 兼容** 选项，输入**API 密钥**并设置相关参数
![[Pasted image 20250624124856.png]]
==**特别说明：**==
<font color="#ff0000">模型 ID 不同，费率不同，请选择适合你的模型 ID。</font>

### 5. 更多客户端

![[Pasted image 20250612165154.png]]

更多客户端，基本操作都是进入设置 --->自定义添加模型 --->设置 APIKEY、API 地址 --->添加模型 ID。

## 六、模型更新的说明

模型 ID 随供应商及时更新，若供应商更新，请联系群主提供新的模型 ID。

![[Pasted image 20250612210001.png]]
