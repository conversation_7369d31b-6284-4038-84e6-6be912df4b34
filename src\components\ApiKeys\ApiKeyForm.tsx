import React, { useState } from 'react';
import { X } from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { ApiKey } from '../../types';

interface ApiKeyFormProps {
  apiKey?: ApiKey | null;
  onClose: () => void;
}

const ApiKeyForm: React.FC<ApiKeyFormProps> = ({ apiKey, onClose }) => {
  const { state, dispatch } = useApp();
  
  const [formData, setFormData] = useState({
    name: apiKey?.name || '',
    modelIds: apiKey?.modelIds || [],
    permissions: apiKey?.permissions || ['read'],
    status: apiKey?.status || 'active',
    rateLimit: apiKey?.rateLimit?.toString() || '100',
    expiresAt: apiKey?.expiresAt ? apiKey.expiresAt.split('T')[0] : ''
  });

  const generateApiKey = () => {
    const prefix = formData.name.toLowerCase().includes('test') ? 'sk-test-' : 'sk-proj-';
    const randomPart = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    return prefix + randomPart;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const newApiKey: ApiKey = {
      id: apiKey?.id || Date.now().toString(),
      name: formData.name,
      key: apiKey?.key || generateApiKey(),
      modelIds: formData.modelIds,
      permissions: formData.permissions as ('read' | 'write' | 'admin')[],
      status: formData.status as ApiKey['status'],
      createdAt: apiKey?.createdAt || new Date().toISOString(),
      expiresAt: formData.expiresAt ? `${formData.expiresAt}T23:59:59Z` : undefined,
      lastUsed: apiKey?.lastUsed,
      usageCount: apiKey?.usageCount || 0,
      rateLimit: parseInt(formData.rateLimit)
    };

    if (apiKey) {
      dispatch({ type: 'UPDATE_API_KEY', payload: newApiKey });
    } else {
      dispatch({ type: 'ADD_API_KEY', payload: newApiKey });
    }

    onClose();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (name === 'modelIds') {
      const checkbox = e.target as HTMLInputElement;
      const modelId = checkbox.value;
      const newModelIds = checkbox.checked
        ? [...formData.modelIds, modelId]
        : formData.modelIds.filter(id => id !== modelId);
      
      setFormData({ ...formData, modelIds: newModelIds });
    } else if (name === 'permissions') {
      const checkbox = e.target as HTMLInputElement;
      const permission = checkbox.value;
      const newPermissions = checkbox.checked
        ? [...formData.permissions, permission]
        : formData.permissions.filter(p => p !== permission);
      
      setFormData({ ...formData, permissions: newPermissions });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              {apiKey ? '编辑API密钥' : '生成新API密钥'}
            </h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              密钥名称
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="例如：生产环境密钥"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                状态
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="active">活跃</option>
                <option value="inactive">停用</option>
                <option value="expired">已过期</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                速率限制 (请求/分钟)
              </label>
              <input
                type="number"
                name="rateLimit"
                value={formData.rateLimit}
                onChange={handleChange}
                required
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="100"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              过期日期 (可选)
            </label>
            <input
              type="date"
              name="expiresAt"
              value={formData.expiresAt}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              权限
            </label>
            <div className="space-y-2">
              {[
                { value: 'read', label: '只读' },
                { value: 'write', label: '写入' },
                { value: 'admin', label: '管理员' }
              ].map((permission) => (
                <label key={permission.value} className="flex items-center">
                  <input
                    type="checkbox"
                    name="permissions"
                    value={permission.value}
                    checked={formData.permissions.includes(permission.value)}
                    onChange={handleChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">{permission.label}</span>
                </label>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              关联模型
            </label>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {state.models.map((model) => (
                <label key={model.id} className="flex items-center">
                  <input
                    type="checkbox"
                    name="modelIds"
                    value={model.id}
                    checked={formData.modelIds.includes(model.id)}
                    onChange={handleChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    {model.name} ({model.provider})
                  </span>
                </label>
              ))}
            </div>
          </div>

          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {apiKey ? '更新密钥' : '生成密钥'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ApiKeyForm;