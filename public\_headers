# 全局安全头部
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  Strict-Transport-Security: max-age=31536000; includeSubDomains
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data: blob: https: http:; connect-src 'self' https:

# 静态资源缓存
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# 图片缓存
/*.png
  Cache-Control: public, max-age=31536000

/*.jpg
  Cache-Control: public, max-age=31536000

/*.jpeg
  Cache-Control: public, max-age=31536000

/*.gif
  Cache-Control: public, max-age=31536000

/*.svg
  Cache-Control: public, max-age=31536000

/*.ico
  Cache-Control: public, max-age=31536000

# CSS 和 JS 缓存
/*.css
  Cache-Control: public, max-age=31536000

/*.js
  Cache-Control: public, max-age=31536000

# 字体缓存
/*.woff
  Cache-Control: public, max-age=31536000

/*.woff2
  Cache-Control: public, max-age=31536000

/*.ttf
  Cache-Control: public, max-age=31536000

/*.eot
  Cache-Control: public, max-age=31536000

# SEO 文件
/sitemap.xml
  Cache-Control: public, max-age=86400
  Content-Type: application/xml

/robots.txt
  Cache-Control: public, max-age=86400
  Content-Type: text/plain

# HTML 文件
/*.html
  Cache-Control: public, max-age=0, must-revalidate

# JSON 文件
/*.json
  Cache-Control: public, max-age=86400
  Content-Type: application/json
