export interface Model {
  id: string;
  name: string;
  provider: string;
  type: 'text' | 'image' | 'audio' | 'multimodal';
  status: 'active' | 'inactive' | 'maintenance';
  apiEndpoint: string;
  maxTokens: number;
  costPerToken: number;
  description: string;
  createdAt: string;
  lastUsed: string;
  totalRequests: number;
  successRate: number;
}

export interface ApiKey {
  id: string;
  name: string;
  key: string;
  modelIds: string[];
  permissions: ('read' | 'write' | 'admin')[];
  status: 'active' | 'inactive' | 'expired';
  createdAt: string;
  expiresAt?: string;
  lastUsed?: string;
  usageCount: number;
  rateLimit: number;
}

export interface Usage {
  id: string;
  modelId: string;
  apiKeyId: string;
  timestamp: string;
  tokens: number;
  cost: number;
  responseTime: number;
  status: 'success' | 'error';
  errorMessage?: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user' | 'viewer';
  avatar?: string;
}