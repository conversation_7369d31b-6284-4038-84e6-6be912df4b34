以下是该页面中部分模型 ID 及其对应的 Input 和 Output 费率（或每次调用价格）:

**OpenAI 模型**
* **o3-mini**: Input: $1.1 / M tokens, Output: $4.4 / M tokens
* **chatgpt-4o-latest**: Input: $5 / M tokens, Output: $15 / M tokens
* **gpt-4o-2024-11-20**: Input: $2.5 / M tokens, Output: $10 / M tokens
* **gpt-4o-2024-08-06**: Input: $2.5 / M tokens, Output: $10 / M tokens
* **gpt-4o-2024-05-13**: Input: $2.5 / M tokens, Output: $7.5 / M tokens
* **gpt-4o**: Input: $2.5 / M tokens, Output: $7.5 / M tokens
* **gpt-4o-mini**: Input: $0.15 / M tokens, Output: $0.6 / M tokens
* **gpt-4o-mini-2024-07-18**: Input: $0.15 / M tokens, Output: $0.6 / M tokens
* **gpt-4-turbo**: Input: $10 / M tokens, Output: $30 / M tokens
* **gpt-4-turbo-2024-04-09**: Input: $10 / M tokens, Output: $30 / M tokens
* **gpt-4-vision-preview**: Input: $10 / M tokens, Output: $20 / M tokens
* **gpt-4-1106-preview**: Input: $10 / M tokens, Output: $20 / M tokens
* **gpt-4-0125-preview**: Input: $10 / M tokens, Output: $20 / M tokens
* **gpt-4-turbo-preview**: Input: $10 / M tokens, Output: $30 / M tokens
* **gpt-4**: Input: $30 / M tokens, Output: $60 / M tokens
* **gpt-4-0613**: Input: $30 / M tokens, Output: $60 / M tokens
* **gpt-4-32k**: Input: $60 / M tokens, Output: $120 / M tokens
* **gpt-4-32k-0613**: Input: $60 / M tokens, Output: $120 / M tokens
* **gpt-4-32k-0314**: Input: $60 / M tokens, Output: $120 / M tokens
* **gpt-3.5-turbo**: Input: $1.5 / M tokens, Output: $1.995 / M tokens
* **gpt-3.5-turbo-0613**: Input: $1.5 / M tokens, Output: $2 / M tokens
* **gpt-3.5-turbo-0301**: Input: $1.5 / M tokens, Output: $2 / M tokens
* **gpt-3.5-turbo-1106**: Input: $1 / M tokens, Output: $2 / M tokens
* **gpt-3.5-turbo-0125**: Input: $0.5 / M tokens, Output: $1.5 / M tokens
* **gpt-3.5-turbo-instruct**: Input: $1.5 / M tokens, Output: $2 / M tokens
* **gpt-3.5-turbo-16k**: Input: $3 / M tokens, Output: $4 / M tokens
* **gpt-3.5-turbo-16k-0613**: Input: $3 / M tokens, Output: $4 / M tokens
* **tts-1**: Input: $15 / M tokens, Output: $15 / M tokens
* **tts-1-1106**: Input: $15 / M tokens, Output: $15 / M tokens
* **tts-1-hd**: Input: $30 / M tokens, Output: $30 / M tokens
* **tts-1-hd-1106**: Input: $30 / M tokens, Output: $30 / M tokens
* **text-embedding-ada-002**: Input: $0.1 / M tokens, Output: $0.1 / M tokens
* **text-embedding-3-small**: Input: $0.02 / M tokens, Output: $0.02 / M tokens
* **text-embedding-3-large**: Input: $0.13 / M tokens, Output: $0.13 / M tokens
* **o1-preview**: Price: $0.6 / 次
* **o1-preview-2024-09-12**: Price: $0.6 / 次
* **o1-mini**: Price: $0.15 / 次
* **o1-mini-2024-09-12**: Price: $0.15 / 次
* **o1**: Price: $0.6 / 次
* **o1-pro**: Price: $1.2 / 次
* **gpt-4.5-preview-2025-02-27**: Input: $600 / M tokens, Output: $1200 / M tokens
* **gpt-4o-image**: Price: $0.04 / 次
* **gpt-image-1-vip**: Price: $0.1 / 次
* **gpt-4o-image-vip**: Price: $0.1 / 次
* **gpt-image-1**: Price: $0.04 / 次
* **o3-pro**: Price: $1.2 / 次
* **advanced-voice**: Price: $2 / 次
* **openai-gpt-4o-2024-08-06**: Input: $23 / M tokens, Output: $92 / M tokens
* **openai-gpt-4o-mini**: Input: $1.38 / M tokens, Output: $5.52 / M tokens
* **gpt-4-gizmo**: Input: $30 / M tokens, Output: $30 / M tokens
* **gpt-4o-dalle**: Price: 点击查看价格详情
* **o4-mini**: Input: $1.1 / M tokens, Output: $4.4 / M tokens
* **o3-pro-all**: Price: $1.2 / 次
* **gpt-5-gizmo***: Input: $60 / M tokens, Output: $60 / M tokens
* **gpt-4.1**: Input: $2 / M tokens, Output: $8 / M tokens
* **o3**: Price: $0.6 / 次
* **gpt-4.1-mini**: Input: $0.4 / M tokens, Output: $1.6 / M tokens
* **gpt-4o-search**: Input: $5 / M tokens, Output: $15 / M tokens
* **gpt-4-search**: Input: $30 / M tokens, Output: $60 / M tokens
* **openai-gpt-4o**: Input: $23 / M tokens, Output: $69 / M tokens
* **gpt-4.1-nano**: Input: $0.1 / M tokens, Output: $0.4 / M tokens
* **o3-all**: Price: $0.6 / 次
* **o3-pro-async**: Price: $1.2 / 次
* **gpt-4-gizmo-***: Input: $30 / M tokens, Output: $30 / M tokens
* **gpt-4-all**: Input: $30 / M tokens, Output: $30 / M tokens
* **gpt-4o-all**: Input: $5 / M tokens, Output: $15 / M tokens
* **gpt-4-dalle**: Price: 点击查看价格详情
* **gpt-4-v**: Input: $30 / M tokens, Output: $60 / M tokens
* **search-gpts**: Input: $1 / M tokens, Output: $1 / M tokens
* **search-gpts-chat**: Input: $1 / M tokens, Output: $1 / M tokens
* **o1-mini-all**: Price: $0.15 / 次
* **o1-preview-all**: Price: $0.6 / 次
* **o1-all**: Price: $0.6 / 次
* **o1-pro-all**: Price: $1.2 / 次
* **o4-mini-dr**: Price: $0.4 / 次
* **o3-mini-all**: Input: $1.1 / M tokens, Output: $4.4 / M tokens
* **o3-mini-high**: Price: $0.15 / 次
* **o3-mini-high-all**: Price: $0.15 / 次

**Claude 模型**
* **claude-3-5-sonnet-20240620**: Input: $6 / M tokens, Output: $30 / M tokens
* **claude-3-5-sonnet-20241022**: Input: $6 / M tokens, Output: $30 / M tokens
* **claude-3-5-haiku-20241022**: Input: $2 / M tokens, Output: $10 / M tokens
* **claude-3-haiku-20240307**: Input: $0.5 / M tokens, Output: $2.5 / M tokens
* **claude-3-sonnet-20240229**: Input: $18 / M tokens, Output: $90 / M tokens
* **claude-3-opus-20240229**: Input: $30 / M tokens, Output: $150 / M tokens
* **claude-3-5-sonnet-all**: Input: $6 / M tokens, Output: $30 / M tokens
* **claude-2**: Input: $22.04 / M tokens, Output: $66.12 / M tokens
* **claude-opus-4-20250514**: Input: $30 / M tokens, Output: $150 / M tokens
* **claude-opus-4-20250514-thinking**: Input: $30 / M tokens, Output: $150 / M tokens
* **claude-sonnet-4-20250514**: Input: $6 / M tokens, Output: $30 / M tokens
* **claude-sonnet-4-20250514-thinking**: Input: $6 / M tokens, Output: $30 / M tokens
* **claude-3-haiku-20240307-all**: Input: $60 / M tokens, Output: $300 / M tokens
* **claude-3-7-sonnet-thinking**: Input: $6 / M tokens, Output: $30 / M tokens
* **claude-3-sonnet-20240229-all**: Input: $60 / M tokens, Output: $300 / M tokens
* **claude-3-haiku-20241022**: Input: $0.5 / M tokens, Output: $2.5 / M tokens
* **claude-3-haiku-all**: Input: $0.5 / M tokens, Output: $2.5 / M tokens
* **claude-opus-4-thinking**: Input: $60 / M tokens, Output: $300 / M tokens
* **claude-3-7-sonnet-20250219**: Input: $6 / M tokens, Output: $30 / M tokens
* **claude-1-100k**: Input: $3.26 / M tokens, Output: $3.26 / M tokens
* **claude-3-sonnet-all**: Input: $6 / M tokens, Output: $30 / M tokens
* **claude-3-5-sonnet-coder**: Input: $6 / M tokens, Output: $30 / M tokens

**Google 模型**
* **gemini-2.0-flash-exp**: Input: $1.2 / M tokens, Output: $4.8 / M tokens
* **gemini-2.0-flash-thinking-exp-1219**: Input: $1.2 / M tokens, Output: $4.8 / M tokens
* **gemini-exp-1206**: Input: $4 / M tokens, Output: $16 / M tokens
* **gemini-exp-1121**: Input: $4 / M tokens, Output: $16 / M tokens
* **gemini-exp-1114**: Input: $4 / M tokens, Output: $16 / M tokens
* **gemini-pro**: Input: $2 / M tokens, Output: $6 / M tokens
* **gemini-pro-vision**: Input: $4 / M tokens, Output: $12 / M tokens
* **gemini-1.5-pro**: Input: $7 / M tokens, Output: $21 / M tokens
* **gemini-1.5-flash**: Input: $0.7 / M tokens, Output: $2.1 / M tokens
* **google-palm**: Input: $2 / M tokens, Output: $2 / M tokens
* **gemini-1.5-pro-001**: Input: $4 / M tokens, Output: $16 / M tokens
* **gemini-1.5-pro-002**: Input: $4 / M tokens, Output: $16 / M tokens
* **gemini-1.5-pro-exp-0827**: Input: $3.5 / M tokens, Output: $14 / M tokens
* **gemini-1.5-pro-exp-0801**: Input: $3.5 / M tokens, Output: $14 / M tokens
* **gemini-2.0-flash-exp-image-generation**: Input: $2.5 / M tokens, Output: $5 / M tokens
* **gemini-2.5-pro-preview-06-05**: Input: $2.5 / M tokens, Output: $12.5 / M tokens
* **gemini-2.5-flash-all**: Input: $0.3 / M tokens, Output: $2.4 / M tokens
* **gemini-2.5-pro-all**: Input: $2.5 / M tokens, Output: $20 / M tokens
* **gemini-2.5-flash-deepsearch**: Input: $6 / M tokens, Output: $48 / M tokens
* **gemini-2.5-pro-deepsearch**: Input: $10 / M tokens, Output: $80 / M tokens
* **veo3**: Price: $2 / 次
* **veo3-pro-frames**: Price: $10 / 次
* **veo3-pro**: Price: $10 / 次
* **veo3-fast**: Price: $2 / 次
* **veo2-pro**: Price: $10 / 次
* **veo2-fast-components**: Price: $1 / 次
* **veo2-fast-frames**: Price: $1 / 次
* **gemini-2.0-flash-lite-preview-02-05**: Input: $0.15 / M tokens, Output: $0.45 / M tokens
* **gemini-2.5-pro-exp-03-25**: Input: $2.5 / M tokens, Output: $10 / M tokens
* **veo2-fast**: Price: $1 / 次
* **gemini-2.0-pro-exp-02-05**: Input: $5 / M tokens, Output: $15 / M tokens
* **gemini-2.5-flash-preview-04-17**: Input: $0.3 / M tokens, Output: $1.2 / M tokens
* **veo2**: Price: $1 / 次
* **gemini-2.5-pro-preview-05-06**: Input: $3 / M tokens, Output: $15 / M tokens

**DeepSeek 模型**
* **deepseek-chat**: Input: $1 / M tokens, Output: $4 / M tokens
* **deepseek-coder**: Input: $1 / M tokens, Output: $4 / M tokens
* **deepseek-reasoner**: Input: $1.65 / M tokens, Output: $6.6 / M tokens
* **deepseek-r1deepseek-v3**: Input: $0.81 / M tokens, Output: $3.24 / M tokens
* **deepseek-v3-all**: Input: $0.81 / M tokens, Output: $3.24 / M tokens
* **deepseek-r1-s**: Input: $1.65 / M tokens, Output: $6.6 / M tokens
* **deepseek-r1-all**: Input: $1.65 / M tokens, Output: $6.6 / M tokens
* **deepseek-r1-0528**: Input: $1.65 / M tokens, Output: $6.6 / M tokens
* **deepseek-v3-0324**: Input: $1 / M tokens, Output: $4 / M tokens
* **deepseek-r1**: Input: $1.65 / M tokens, Output: $6.6 / M tokens
* **deepseek-v3-250324**: Input: $1 / M tokens, Output: $4 / M tokens
* **deepseek-reasoner-all**: Input: $1.65 / M tokens, Output: $6.6 / M tokens

**xAI 模型**
* **grok-2-1212**: Input: $2 / M tokens, Output: $10 / M tokens
* **grok-beta**: Input: $2 / M tokens, Output: $6 / M tokens
* **grok-3**: Input: $2 / M tokens, Output: $10 / M tokens
* **grok-3-deepsearch**: Input: $2 / M tokens, Output: $2 / M tokens
* **grok-3-reasoner**: Input: $2 / M tokens, Output: $10 / M tokens
* **grok-3-deepersearch-r**: Input: $2 / M tokens, Output: $2 / M tokens
* **grok-3-deepsearch-r**: Input: $2 / M tokens, Output: $2 / M tokens
* **grok-3-reasoner-r**: Input: $2 / M tokens, Output: $10 / M tokens
* **grok-3-think**: Input: $2 / M tokens, Output: $10 / M tokens
* **grok-3-deepersearch**: Input: $2 / M tokens, Output: $2 / M tokens
