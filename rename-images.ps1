# PowerShell脚本：重命名图片文件，去掉空格和特殊字符
# 运行前请确保在项目根目录

Write-Host "开始重命名图片文件..." -ForegroundColor Green

# 手动重命名文件
$files = @(
    @{old='20250612161534.png'; new='immersive-translate-step1.png'}
    @{old='Pasted image 20250612160644.png'; new='chatbox-step1.png'}
    @{old='Pasted image 20250612161015.png'; new='step-1015.png'}
    @{old='Pasted image 20250612161156.png'; new='step-1156.png'}
    @{old='Pasted image 20250612161708.png'; new='step-1708.png'}
    @{old='Pasted image 20250612162457.png'; new='step-2457.png'}
    @{old='Pasted image 20250612162621.png'; new='step-2621.png'}
    @{old='Pasted image 20250612162743.png'; new='cherry-studio-step1.png'}
    @{old='Pasted image 20250612162942.png'; new='cherry-studio-step2.png'}
    @{old='Pasted image 20250612163041.png'; new='step-3041.png'}
    @{old='Pasted image 20250612165154.png'; new='step-5154.png'}
    @{old='Pasted image 20250613234642.png'; new='step-234642.png'}
    @{old='Pasted image 20250624124856.png'; new='step-124856.png'}
    @{old='PixPin_2025-06-13_23-43-18.png'; new='immersive-translate-step3.png'}
    @{old='PixPin_2025-06-13_23-47-36.png'; new='step-234736.png'}
    @{old='PixPin_2025-06-13_23-49-15.png'; new='step-234915.png'}
    @{old='PixPin_2025-06-13_23-50-14.png'; new='step-235014.png'}
    @{old='PixPin_2025-06-13_23-57-55.png'; new='chatbox-step2.png'}
)

# 重命名public目录中的文件
foreach ($file in $files) {
    $oldName = $file.old
    $newName = $file.new
    $oldPath = "public\$oldName"

    if (Test-Path $oldPath) {
        Rename-Item -Path $oldPath -NewName $newName
        Write-Host "已重命名: $oldName -> $newName" -ForegroundColor Yellow
    } else {
        Write-Host "文件不存在: $oldPath" -ForegroundColor Red
    }
}

# 重命名dist目录中的文件（如果存在）
if (Test-Path "dist") {
    foreach ($file in $files) {
        $oldName = $file.old
        $newName = $file.new
        $oldPath = "dist\$oldName"

        if (Test-Path $oldPath) {
            Rename-Item -Path $oldPath -NewName $newName
            Write-Host "已重命名dist中的文件: $oldName -> $newName" -ForegroundColor Yellow
        }
    }
}

Write-Host "图片文件重命名完成！" -ForegroundColor Green
Write-Host "请运行 'npm run build' 重新构建项目" -ForegroundColor Cyan
